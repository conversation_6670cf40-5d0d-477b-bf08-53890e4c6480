"""empty message

Revision ID: 96f00ccd92c4
Revises: 
Create Date: 2025-06-21 06:41:53.127245

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '96f00ccd92c4'
down_revision = None
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_ijack():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('alerts', schema=None) as batch_op:
        batch_op.drop_constraint('alerts_power_unit_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('alerts_fk_user_id', type_='foreignkey')
        batch_op.drop_constraint('alerts_fk_gateway_id', type_='foreignkey')
        batch_op.drop_constraint('alerts_fk_power_unit_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'gw', ['gateway_id'], ['id'], referent_schema='public', onupdate='CASCADE', ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom', schema=None) as batch_op:
        batch_op.alter_column('want_sms',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('want_email',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
        batch_op.drop_constraint('alerts_custom_fk_hour', type_='foreignkey')
        batch_op.drop_constraint('alerts_custom_hour_ending_fkey', type_='foreignkey')
        batch_op.drop_constraint('alerts_custom_fk_customer_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'hours', ['hour_ending'], ['hour_ending'], referent_schema='public')
        batch_op.create_foreign_key(None, 'customers', ['customer_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'time_zones', ['time_zone_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('alerts_custom_days_rel', schema=None) as batch_op:
        batch_op.drop_constraint('alerts_custom_days_rel_alerts_custom_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('alerts_custom_days_rel_day_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'alerts_custom', ['alerts_custom_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'days', ['day'], ['day'], referent_schema='public')

    with op.batch_alter_table('alerts_custom_images_rel', schema=None) as batch_op:
        batch_op.drop_constraint('alerts_custom_images_rel_image_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('alerts_custom_images_rel_alerts_custom_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'alerts_custom', ['alerts_custom_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'images', ['image_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom_months_rel', schema=None) as batch_op:
        batch_op.drop_constraint('alerts_custom_months_rel_alerts_custom_id_fkey1', type_='foreignkey')
        batch_op.drop_constraint('alerts_custom_months_rel_month_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'months', ['month'], ['month'], referent_schema='public')
        batch_op.create_foreign_key(None, 'alerts_custom', ['alerts_custom_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom_structure_rel', schema=None) as batch_op:
        batch_op.drop_constraint('alerts_custom_structure_rel_alerts_custom_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('alerts_custom_structure_rel_structure_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'alerts_custom', ['alerts_custom_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom_user_rel', schema=None) as batch_op:
        batch_op.drop_constraint('alerts_custom_user_rel_alerts_custom_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'alerts_custom', ['alerts_custom_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('alerts_sent', schema=None) as batch_op:
        batch_op.drop_index('alerts_sent_index_timestamp_utc_gateway')
        batch_op.drop_table_comment(
        existing_comment='For saving a record of alerts sent'
    )

    with op.batch_alter_table('alerts_sent_maint', schema=None) as batch_op:
        batch_op.drop_constraint('alerts_sent_maint_timestamp_utc_customer_id_email_type_key', type_='unique')
        batch_op.drop_constraint('alerts_sent_maint_fk_customer_id', type_='foreignkey')
        batch_op.drop_constraint('alerts_sent_maint_email_type_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'alerts_sent_maint_email_types', ['email_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'customers', ['customer_id'], ['id'], referent_schema='public')
        batch_op.drop_column('customer')
        batch_op.drop_column('email_type')

    with op.batch_alter_table('alerts_sent_maint_users', schema=None) as batch_op:
        batch_op.alter_column('dev_test_prd',
               existing_type=sa.CHAR(length=11),
               nullable=True)
        batch_op.drop_constraint('alerts_sent_maint_users_alerts_sent_maint_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'alerts_sent_maint', ['alerts_sent_maint_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.drop_column('msg_type')

    with op.batch_alter_table('alerts_sent_other', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='For saving datetimes when alerts were sent, for other, random stuff like users with no phone numbers, or units with no alerts setup.'
    )

    with op.batch_alter_table('alerts_sent_users', schema=None) as batch_op:
        batch_op.alter_column('msg_type',
               existing_type=sa.VARCHAR(),
               comment=None,
               existing_comment='email, SMS, phone, WhatsApp, etc',
               existing_nullable=True)
        batch_op.drop_constraint('alerts_sent_users_alerts_sent_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'alerts_sent', ['alerts_sent_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('alerts_sent_wait_okay', schema=None) as batch_op:
        batch_op.alter_column('alert_type_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.drop_constraint('alerts_sent_wait_okay_power_unit_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('alerts_sent_wait_okay_alert_type_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'alerts_types', ['alert_type_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('alerts_types', schema=None) as batch_op:
        batch_op.drop_column('abbrev')

    with op.batch_alter_table('application_types', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'unit_types', ['unit_type_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('application_upload_files', schema=None) as batch_op:
        batch_op.drop_constraint('application_upload_files_application_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'applications', ['application_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('applications', schema=None) as batch_op:
        batch_op.drop_constraint('applications_application_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('applications_province_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('applications_customer_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('applications_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'customers', ['customer_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'application_types', ['application_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('bom_base_powerunit_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_base_powerunit_part_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('bom_base_powerunit_part_rel_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'bom_base_powerunit', ['finished_good_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('bom_base_powerunit_power_unit_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_base_powerunit_power_unit_type_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_unit_types', ['power_unit_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'bom_base_powerunit', ['finished_good_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('bom_dgas_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_dgas_model_type_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'bom_dgas', ['finished_good_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('bom_dgas_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_dgas_part_rel_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('bom_dgas_part_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'bom_dgas', ['finished_good_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('bom_powerunit_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_powerunit_part_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('bom_powerunit_part_rel_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'bom_powerunit', ['finished_good_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('bom_powerunit_power_unit_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_powerunit_power_unit_type_finished_good_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('bom_powerunit_power_unit_type_power_unit_type_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_unit_types', ['power_unit_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'bom_powerunit', ['finished_good_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('bom_pricing_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_pricing_model_type_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('bom_pricing_model_type_rel_model_type_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'bom_pricing', ['finished_good_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('bom_pricing_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_pricing_part_rel_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('bom_pricing_part_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'bom_pricing', ['finished_good_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('bom_pump_top_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_pump_top_model_type_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'bom_pump_top', ['finished_good_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('bom_pump_top_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_pump_top_part_rel_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('bom_pump_top_part_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'bom_pump_top', ['finished_good_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('bom_structure_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_structure_model_type_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'bom_structure', ['finished_good_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('bom_structure_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint('bom_structure_part_rel_finished_good_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('bom_structure_part_rel_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'bom_structure', ['finished_good_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('calculators', schema=None) as batch_op:
        batch_op.drop_constraint('calculators_power_unit_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('calculators_model_type_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'power_unit_types', ['power_unit_type_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('calories', schema=None) as batch_op:
        batch_op.drop_constraint('calories_calorie_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('calories_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'calorie_types', ['calorie_type_id'], ['id'], referent_schema='public')
        batch_op.drop_column('description')

    with op.batch_alter_table('career_applications', schema=None) as batch_op:
        batch_op.drop_constraint('career_applications_fk_user_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('career_applications_files_rel', schema=None) as batch_op:
        batch_op.drop_constraint('career_application_files_career_application_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('career_applications_files_rel_career_application_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('career_applications_files_rel_career_file_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('career_application_files_career_file_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'career_files', ['career_file_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'career_applications', ['career_application_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('career_files', schema=None) as batch_op:
        batch_op.drop_constraint('career_files_fk_user_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('cities', schema=None) as batch_op:
        batch_op.drop_constraint('cities_county_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('cities_province_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'counties', ['county_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('compression_images', schema=None) as batch_op:
        batch_op.drop_index('compression_images_index_cluster_id_ml_version')
        batch_op.drop_constraint('compression_images_fk_pattern_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'compression_patterns', ['pattern_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('contact_form', schema=None) as batch_op:
        batch_op.drop_constraint('contact_form_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('counties', schema=None) as batch_op:
        batch_op.drop_constraint('counties_province_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('currencies', schema=None) as batch_op:
        batch_op.alter_column('fx_rate_cad_per',
               existing_type=sa.NUMERIC(),
               comment=None,
               existing_comment='CAD cost per unit of this currency',
               existing_nullable=False,
               existing_server_default=sa.text('1'))
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('currencies_rates', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Primary key',
               existing_nullable=False,
               autoincrement=True)
        batch_op.alter_column('currency_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Foreign key to currencies table',
               existing_nullable=False)
        batch_op.alter_column('rate_date',
               existing_type=sa.DATE(),
               comment=None,
               existing_comment='Date of the exchange rate',
               existing_nullable=False)
        batch_op.alter_column('fx_rate_cad_per',
               existing_type=sa.NUMERIC(precision=10, scale=6),
               comment=None,
               existing_comment='Exchange rate: CAD per unit of currency',
               existing_nullable=False)
        batch_op.alter_column('source',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='Data source (e.g., exchangerate-api.com)',
               existing_nullable=False,
               existing_server_default=sa.text("'exchangerate-api.com'::character varying"))
        batch_op.drop_index('idx_currencies_rates_currency_date')
        batch_op.drop_index('idx_currencies_rates_currency_id')
        batch_op.drop_index('idx_currencies_rates_rate_date')
        batch_op.drop_constraint('fk_currencies_rates_currency_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'currencies', ['currency_id'], ['id'], referent_schema='public')
        batch_op.drop_table_comment(
        existing_comment='Historical currency exchange rates'
    )

    with op.batch_alter_table('cust_sub_groups', schema=None) as batch_op:
        batch_op.drop_constraint('cust_sub_groups_customer_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'customers', ['customer_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('customers', schema=None) as batch_op:
        batch_op.drop_constraint('customers_country_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('customers_province_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key('customers_accounting_contact_id_fkey', 'users', ['accounting_contact_id'], ['id'], referent_schema='public', initially='DEFERRED', deferrable=True, use_alter=True)

    with op.batch_alter_table('cycle_count_items', schema=None) as batch_op:
        batch_op.drop_index('idx_cycle_count_items_count')
        batch_op.drop_index('idx_cycle_count_items_location')
        batch_op.drop_index('idx_cycle_count_items_part')
        batch_op.drop_constraint('cycle_count_items_cycle_count_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('cycle_count_items_counted_by_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('cycle_count_items_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('cycle_count_items_location_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('cycle_count_items_adjustment_movement_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['counted_by_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'inventory_movements', ['adjustment_movement_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'warehouse_locations', ['location_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'cycle_counts', ['cycle_count_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('cycle_counts', schema=None) as batch_op:
        batch_op.drop_constraint('cycle_counts_count_number_key', type_='unique')
        batch_op.drop_index('idx_cycle_count_number')
        batch_op.drop_index('idx_cycle_count_scheduled')
        batch_op.drop_index('idx_cycle_count_status')
        batch_op.drop_index('idx_cycle_count_warehouse')
        batch_op.create_index(batch_op.f('ix_public_cycle_counts_count_number'), ['count_number'], unique=True)
        batch_op.drop_constraint('cycle_counts_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('cycle_counts_created_by_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('cycle_counts_assigned_to_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['assigned_to_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'users', ['created_by_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('days_of_week', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               type_=sa.SMALLINT(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('days_of_week_id_seq'::regclass)"))
        batch_op.create_unique_constraint(None, ['name'])

    with op.batch_alter_table('error_logs', schema=None) as batch_op:
        batch_op.drop_constraint('error_logs_user_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('error_logs_resolved_by_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['resolved_by_user_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='SET NULL')

    with op.batch_alter_table('flask_dance_oauth', schema=None) as batch_op:
        batch_op.drop_constraint('flask_dance_oauth_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('geocoding_cache', schema=None) as batch_op:
        batch_op.alter_column('lat_rounded',
               existing_type=sa.NUMERIC(precision=8, scale=5),
               comment=None,
               existing_comment='Latitude rounded to 5 decimal places for caching efficiency',
               existing_nullable=False)
        batch_op.alter_column('lon_rounded',
               existing_type=sa.NUMERIC(precision=8, scale=5),
               comment=None,
               existing_comment='Longitude rounded to 5 decimal places for caching efficiency',
               existing_nullable=False)
        batch_op.alter_column('country_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Foreign key reference to countries table',
               existing_nullable=True)
        batch_op.alter_column('province_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Foreign key reference to provinces table',
               existing_nullable=True)
        batch_op.alter_column('locality',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='City or locality name from geocoding result',
               existing_nullable=True)
        batch_op.alter_column('confidence_score',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='Confidence score of the geocoding result (0.0 to 1.0)',
               existing_nullable=True,
               existing_server_default=sa.text('1.0'))
        batch_op.alter_column('data_source',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='Source of the geocoding data (nominatim, google, etc.)',
               existing_nullable=True,
               existing_server_default=sa.text("'nominatim'::character varying"))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='Timestamp when the cache entry was created',
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='Timestamp when the cache entry was last updated',
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
        batch_op.drop_index('idx_geocoding_cache_coordinates')
        batch_op.drop_index('idx_geocoding_cache_country_id')
        batch_op.drop_index('idx_geocoding_cache_created_at')
        batch_op.drop_index('idx_geocoding_cache_province_id')
        batch_op.drop_constraint('geocoding_cache_province_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('geocoding_cache_country_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public')
        batch_op.drop_table_comment(
        existing_comment='Cache table for reverse geocoding results to improve performance and reduce external API calls'
    )

    with op.batch_alter_table('gw', schema=None) as batch_op:
        batch_op.drop_constraint('gw_id_unique', type_='unique')
        batch_op.drop_index('gw_index_gateway_structure_id_power_unit_id')
        batch_op.drop_constraint('gw_gateway_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('gw_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('gw_power_unit_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'gateway_types', ['gateway_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('gw_info', schema=None) as batch_op:
        batch_op.alter_column('timestamp_utc_last_reported',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
        batch_op.alter_column('has_slave',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
        batch_op.drop_constraint('gw_info_gateway_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'gw', ['gateway_id'], ['id'], referent_schema='public')
        batch_op.drop_column('gw_type_reported')

    with op.batch_alter_table('gw_not_connected_dont_worry', schema=None) as batch_op:
        batch_op.drop_constraint('gw_not_connected_dont_worry_gateway_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'gw', ['gateway_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('gw_tested_cellular', schema=None) as batch_op:
        batch_op.alter_column('timestamp_utc',
               existing_type=sa.DATE(),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False)
        batch_op.drop_constraint('gw_tested_cellular_gateway_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('gw_tested_cellular_network_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'gw', ['gateway_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'gw_cell_networks', ['network_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('hours', schema=None) as batch_op:
        batch_op.alter_column('hour_ending',
               existing_type=sa.SMALLINT(),
               nullable=True,
               existing_server_default=sa.text('1'))

    with op.batch_alter_table('inventory_ledger', schema=None) as batch_op:
        batch_op.drop_constraint('inventory_ledger_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_ledger_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_ledger_reversal_of_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_ledger_created_by_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'inventory_ledger', ['reversal_of_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='RESTRICT')
        batch_op.create_foreign_key(None, 'users', ['created_by_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='RESTRICT')

    with op.batch_alter_table('inventory_movements', schema=None) as batch_op:
        batch_op.drop_index('idx_movement_number')
        batch_op.drop_index('idx_movement_reference')
        batch_op.drop_index('idx_movement_type')
        batch_op.drop_constraint('inventory_movements_movement_number_key', type_='unique')
        batch_op.create_index(batch_op.f('ix_public_inventory_movements_movement_number'), ['movement_number'], unique=True)
        batch_op.drop_constraint('inventory_movements_work_order_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_movements_to_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_movements_created_by_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_movements_from_location_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_movements_from_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_movements_to_location_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_movements_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_movements_reversal_movement_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='RESTRICT')
        batch_op.create_foreign_key(None, 'inventory_movements', ['reversal_movement_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'warehouses', ['from_warehouse_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['created_by_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'warehouse_locations', ['from_location_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'warehouse_locations', ['to_location_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'warehouses', ['to_warehouse_id'], ['id'], referent_schema='public')
        batch_op.drop_column('work_order_part_id')

    with op.batch_alter_table('inventory_reservations', schema=None) as batch_op:
        batch_op.drop_index('idx_reservation_number')
        batch_op.drop_index('idx_reservation_part_warehouse')
        batch_op.drop_constraint('inventory_reservations_reservation_number_key', type_='unique')
        batch_op.create_index(batch_op.f('ix_public_inventory_reservations_reservation_number'), ['reservation_number'], unique=True)
        batch_op.drop_constraint('inventory_reservations_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_reservations_work_order_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_reservations_cancelled_by_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_reservations_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_reservations_warehouse_to_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('inventory_reservations_created_by_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['created_by_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_to_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['cancelled_by_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='RESTRICT')
        batch_op.drop_column('work_order_part_id')

    with op.batch_alter_table('maintenance', schema=None) as batch_op:
        batch_op.alter_column('maintenance_type_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.drop_constraint('maintenance_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('maintenance_maintenance_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('maintenance_power_unit_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'maintenance_types', ['maintenance_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('modbus_holding_registers', schema=None) as batch_op:
        batch_op.alter_column('holding_reg_40k',
               existing_type=sa.INTEGER(),
               type_=sa.SMALLINT(),
               existing_nullable=False)
        batch_op.drop_constraint('modbus_holding_registers_abbrev_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'map_abbrev_item', ['abbrev_id'], ['id'], referent_schema='public')
        batch_op.drop_table_comment(
        existing_comment='Mapping the abbrev/metric from the map_abbrev_item table to the holding register and number of registers'
    )

    with op.batch_alter_table('model_types', schema=None) as batch_op:
        batch_op.drop_constraint('model_types_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('model_types_unit_type_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'unit_types', ['unit_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('model_types_options', schema=None) as batch_op:
        batch_op.drop_constraint('model_types_options_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('model_types_options_rel', schema=None) as batch_op:
        batch_op.drop_constraint('model_types_options_rel_option_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('model_types_options_rel_model_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['option_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'parts', ['model_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('model_types_parts_pm_seal_kits_rel', schema=None) as batch_op:
        batch_op.drop_constraint('model_types_parts_pm_seal_kits_rel_model_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('model_types_parts_pm_seal_kits_rel_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('model_types_parts_rel', schema=None) as batch_op:
        batch_op.drop_constraint('model_types_parts_rel_model_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('model_types_parts_rel_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('mqtt_messages', schema=None) as batch_op:
        batch_op.alter_column('rc',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='return code from MQTT operation',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('rc_message',
               existing_type=sa.VARCHAR(),
               comment=None,
               existing_comment='message from MQTT operation (e.g. "MQTT_ERR_SUCCESS")',
               existing_nullable=False)
        batch_op.drop_constraint('mqtt_messages_gateway_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'gw', ['gateway_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('part_categories', schema=None) as batch_op:
        batch_op.drop_index('idx_part_categories_code')
        batch_op.drop_constraint('part_categories_code_key', type_='unique')
        batch_op.create_index(batch_op.f('ix_public_part_categories_code'), ['code'], unique=True)
        batch_op.drop_constraint('part_categories_parent_category_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'part_categories', ['parent_category_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('parts', schema=None) as batch_op:
        batch_op.alter_column('no_delete',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               comment=None,
               existing_comment='This indicates whether the part should remain in the database even if it\'s no longer used in the BOM Master spreadsheet. Things like "miscellaneous" for the work order part line items, should be "true" for "no_delete"',
               existing_server_default=sa.text('false'))
        batch_op.alter_column('is_usd',
               existing_type=sa.BOOLEAN(),
               nullable=False)
        batch_op.alter_column('category_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Foreign key to part_categories table for organizing parts',
               existing_nullable=True)
        batch_op.alter_column('unit_of_measure',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='Unit of measure (EACH, LB, KG, etc.)',
               existing_nullable=False,
               existing_server_default=sa.text("'EACH'::character varying"))
        batch_op.alter_column('unit_cost',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment=None,
               existing_comment='Computed column: cost per unit in CAD (references cost_cad)',
               existing_nullable=True)
        batch_op.alter_column('is_serialized',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='Whether this part requires serial number tracking',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('is_lot_tracked',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='Whether this part requires lot/batch tracking',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('min_stock_level',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment=None,
               existing_comment='Minimum stock level before reorder alert',
               existing_nullable=True)
        batch_op.alter_column('max_stock_level',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment=None,
               existing_comment='Maximum stock level for inventory control',
               existing_nullable=True)
        batch_op.alter_column('reorder_point',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment=None,
               existing_comment='Inventory level that triggers reorder',
               existing_nullable=True)
        batch_op.alter_column('reorder_quantity',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment=None,
               existing_comment='Quantity to order when reorder point is reached',
               existing_nullable=True)
        batch_op.alter_column('lead_time_days',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Lead time in days for procurement',
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='Whether this part is active in the system',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('is_purchasable',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='Whether this part can be purchased',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('is_sellable',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='Whether this part can be sold',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('created_by_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='User who created this part record',
               existing_nullable=False,
               existing_server_default=sa.text('1'))
        batch_op.alter_column('updated_by_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='User who last updated this part record',
               existing_nullable=False,
               existing_server_default=sa.text('1'))
        batch_op.drop_index('idx_parts_category_id')
        batch_op.drop_index('idx_parts_created_by_id')
        batch_op.drop_index('idx_parts_is_active')
        batch_op.drop_index('idx_parts_is_lot_tracked')
        batch_op.drop_index('idx_parts_is_purchasable')
        batch_op.drop_index('idx_parts_is_sellable')
        batch_op.drop_index('idx_parts_is_serialized')
        batch_op.drop_index('idx_parts_unit_of_measure')
        batch_op.drop_index('idx_parts_updated_by_id')
        batch_op.drop_constraint('parts_unique_part_num', type_='unique')
        batch_op.create_index(batch_op.f('ix_public_parts_part_num'), ['part_num'], unique=True)
        batch_op.drop_constraint('parts_updated_by_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('parts_category_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('parts_created_by_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['updated_by_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['created_by_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'part_categories', ['category_id'], ['id'], referent_schema='public')
        batch_op.create_table_comment(
        'Master table for all inventory parts and items',
        existing_comment=None
    )

    with op.batch_alter_table('power_unit_types', schema=None) as batch_op:
        batch_op.drop_constraint('power_unit_types_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('power_unit_types_filters_rel', schema=None) as batch_op:
        batch_op.drop_constraint('power_unit_types_filters_rel_part_filter_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('power_unit_types_filters_rel_power_unit_type_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_unit_types', ['power_unit_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'part_filters', ['part_filter_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('power_unit_types_options', schema=None) as batch_op:
        batch_op.drop_constraint('power_unit_types_options_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('power_unit_types_parts_rel', schema=None) as batch_op:
        batch_op.drop_constraint('power_unit_types_parts_rel_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('power_unit_types_parts_rel_power_unit_type_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'power_unit_types', ['power_unit_type_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('power_unit_types_power', schema=None) as batch_op:
        batch_op.drop_constraint('power_unit_types_power_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('power_unit_types_speeds', schema=None) as batch_op:
        batch_op.drop_constraint('power_unit_types_speeds_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('power_unit_types_voltage', schema=None) as batch_op:
        batch_op.drop_constraint('power_unit_types_voltage_part_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('power_units', schema=None) as batch_op:
        batch_op.alter_column('website_card_msg',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='Put "Artificial Intelligence" messages on the card tab on RCOM?',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('alerts_edge',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('change_detect_sens',
               existing_type=sa.NUMERIC(),
               nullable=True,
               existing_server_default=sa.text('0.8'))
        batch_op.alter_column('wait_time_mins_spm',
               existing_type=sa.SMALLINT(),
               nullable=True,
               existing_server_default=sa.text('60'))
        batch_op.alter_column('wait_time_mins_hyd_temp',
               existing_type=sa.SMALLINT(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text('60'))
        batch_op.alter_column('wait_time_mins_hyd_oil_lvl',
               existing_type=sa.SMALLINT(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('wait_time_mins_hyd_filt_life',
               existing_type=sa.SMALLINT(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('wait_time_mins_hyd_oil_life',
               existing_type=sa.SMALLINT(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.drop_index('power_units_index_power_unit')
        batch_op.drop_constraint('power_units_fk_power_unit_type_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_unit_types', ['power_unit_type_id'], ['id'], referent_schema='public')
        batch_op.drop_column('gateway_modbus')
        batch_op.drop_column('subnet_modbus')
        batch_op.drop_column('ip_modbus')

    with op.batch_alter_table('power_units_fixed_ip_networks', schema=None) as batch_op:
        batch_op.drop_constraint('power_units_fixed_ip_networks_power_unit_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('power_units_modbus_networks', schema=None) as batch_op:
        batch_op.drop_constraint('power_units_modbus_networks_power_unit_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('profiler_measurements', schema=None) as batch_op:
        batch_op.drop_constraint('profiler_measurements_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('provinces', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('release_notes', schema=None) as batch_op:
        batch_op.drop_column('checksum')

    with op.batch_alter_table('remote_control', schema=None) as batch_op:
        batch_op.alter_column('dev_test_prd',
               existing_type=sa.VARCHAR(),
               type_=sa.TEXT(),
               existing_nullable=False)
        batch_op.drop_constraint('remote_control_user_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('remote_control_power_unit_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_days_of_week_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_derates_days_of_week_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_derates_days_of_week_rel_day_of_week_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'days_of_week', ['day_of_week_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'report_email_derates', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_hours_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_derates_hours_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_derates_hours_rel_hour_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'hours', ['hour'], ['hour'], referent_schema='public')
        batch_op.create_foreign_key(None, 'report_email_derates', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_model_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_derates_model_types_rel_model_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_derates_model_types_rel_report_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'report_email_derates', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_unit_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_derates_unit_types_rel_report_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_derates', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'unit_types', ['unit_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_warehouses_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_derates_warehouses_rel_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_derates_warehouses_rel_report_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_derates', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly', schema=None) as batch_op:
        batch_op.drop_constraint('report_units_down_fk_user_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_days_of_week_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_hourly_days_of_week_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_hourly_days_of_week_rel_day_of_week_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_hourly', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'days_of_week', ['day_of_week_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_hours_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_units_down_hours_rel_fk_hours', type_='foreignkey')
        batch_op.drop_constraint('report_email_hourly_hours_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_hourly_hours_rel_hour_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_units_down_hours_rel_fk_report_units_down', type_='foreignkey')
        batch_op.create_foreign_key(None, 'hours', ['hour'], ['hour'], referent_schema='public')
        batch_op.create_foreign_key(None, 'report_email_hourly', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_model_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_hourly_model_types_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_units_down_model_types_rel_fk_model_types', type_='foreignkey')
        batch_op.drop_constraint('report_units_down_model_types_rel_fk_report_units_down', type_='foreignkey')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'report_email_hourly', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_unit_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_units_down_unit_types_rel_fk_unit_types', type_='foreignkey')
        batch_op.drop_constraint('report_email_hourly_unit_types_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_units_down_unit_types_rel_fk_report_units_down', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_hourly', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'unit_types', ['unit_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_warehouses_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_hourly_warehouses_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_hourly_warehouses_rel_warehouse_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'report_email_hourly', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_inventory', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_inventory_days_of_week_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_inventory_days_of_week_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_inventory_days_of_week_rel_day_of_week_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'days_of_week', ['day_of_week_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'report_email_inventory', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_inventory_hours_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_inventory_hours_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_inventory_hours_rel_hour_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_inventory', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'hours', ['hour'], ['hour'], referent_schema='public')

    with op.batch_alter_table('report_email_inventory_warehouses_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_inventory_warehouses_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_inventory_warehouses_rel_warehouse_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_inventory', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours_model_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_op_hours_model_types_rel_model_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_op_hours_model_types_report_email_op_hours_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_op_hours_model_types_rel_report_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_op_hours', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_op_hours_types_rel_report_email_op_hours_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_op_hours_types_r_report_email_op_hours_type_i_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_op_hours_types_rel_report_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_op_hours_types', ['report_email_op_hours_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'report_email_op_hours', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours_unit_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_op_hours_unit_types_rel_report_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_op_hours_unit_types_rel_unit_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_op_hours_unit_types__report_email_op_hours_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'report_email_op_hours', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'unit_types', ['unit_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours_warehouses_rel', schema=None) as batch_op:
        batch_op.drop_constraint('report_email_op_hours_warehouses_rel_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('report_email_op_hours_warehouses_rel_report_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'report_email_op_hours', ['report_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('sales_taxes', schema=None) as batch_op:
        batch_op.drop_constraint('sales_taxes_province_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('service', schema=None) as batch_op:
        batch_op.drop_constraint('service_service_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('service_structure_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'service_types', ['service_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='SET NULL')

    with op.batch_alter_table('service_clock', schema=None) as batch_op:
        batch_op.alter_column('time_zone_in_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('time_zone_out_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.drop_constraint('service_clock_time_zone_id_in_fkey', type_='foreignkey')
        batch_op.drop_constraint('service_clock_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('service_clock_time_zone_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('service_clock_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('service_clock_time_zone_id_out_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'time_zones', ['time_zone_out_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'time_zones', ['time_zone_in_id'], ['id'], referent_schema='public')
        batch_op.drop_column('time_zone_id')

    with op.batch_alter_table('service_emailees', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public')
        batch_op.drop_table_comment(
        existing_comment="who to email when there's a service request"
    )

    with op.batch_alter_table('sim_cards', schema=None) as batch_op:
        batch_op.alter_column('sim_card',
               existing_type=sa.TEXT(),
               nullable=True)
        batch_op.drop_index('sim_cards_index')
        batch_op.drop_constraint('sim_cards_fk_gateway_id', type_='foreignkey')
        batch_op.drop_constraint('sim_cards_fk_customer_id', type_='foreignkey')
        batch_op.create_foreign_key(None, 'gw', ['gateway_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'customers', ['customer_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('structure_cust_sub_group_rel', schema=None) as batch_op:
        batch_op.drop_constraint('structure_cust_sub_group_rel_cust_sub_group_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structure_cust_sub_group_rel_structure_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'cust_sub_groups', ['cust_sub_group_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('structure_customer_rel', schema=None) as batch_op:
        batch_op.drop_constraint('structure_customer_rel_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structure_customer_rel_customer_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'customers', ['customer_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('structures', schema=None) as batch_op:
        batch_op.alter_column('has_rcom',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment="Certain units like DGAS, and certain customers like XTO, won't have SIM cards\n",
               existing_nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('warehouse_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Default warehouse assigned to this structure for parts inventory',
               existing_nullable=True)
        batch_op.create_unique_constraint(None, ['structure_slave_id'])
        batch_op.drop_constraint('structures_fk_power_unit_id', type_='foreignkey')
        batch_op.drop_constraint('structures_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structures_fk_check_valve_id', type_='foreignkey')
        batch_op.drop_constraint('structures_rod_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structures_fk_model_type_id', type_='foreignkey')
        batch_op.drop_constraint('fk_structures_hyd_piston_type_id', type_='foreignkey')
        batch_op.drop_constraint('structures_barrel_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structures_hyd_piston_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structures_check_valve_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structures_fk_time_zone_id', type_='foreignkey')
        batch_op.drop_constraint('structures_packing_gland_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structures_shuttle_valve_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('structures_fk_structure_slave_id', type_='foreignkey')
        batch_op.drop_constraint('structures_fk_unit_type_id', type_='foreignkey')
        batch_op.drop_constraint('structures_province_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'rods', ['rod_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id_slave'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'unit_types', ['unit_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'hyd_piston_types', ['hyd_piston_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'check_valves', ['check_valve_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'barrels', ['barrel_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'shuttle_valves', ['shuttle_valve_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'time_zones', ['time_zone_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'packing_glands', ['packing_gland_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'structures', ['structure_slave_id'], ['id'], referent_schema='public')
        batch_op.drop_column('prev_maint_reset_hours')
        batch_op.drop_column('max_discharge')
        batch_op.drop_column('max_discharge_temp')
        batch_op.drop_column('max_suction')
        batch_op.drop_column('unogas_egas')
        batch_op.drop_column('prev_maint_reset_date')
        batch_op.drop_column('max_delta_p')
        batch_op.drop_column('max_spm')

    with op.batch_alter_table('surface_images', schema=None) as batch_op:
        batch_op.drop_index('surface_images_index_cluster_id_ml_version')
        batch_op.drop_constraint('surface_images_pattern_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'surface_patterns', ['pattern_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('surface_patterns', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='Contains the surface card training pattern ID and description, for machine learning classification'
    )

    with op.batch_alter_table('time_zones', schema=None) as batch_op:
        batch_op.drop_constraint('time_zones_country_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('user_api_tokens', schema=None) as batch_op:
        batch_op.drop_constraint('user_api_tokens_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_authentication_challenges', schema=None) as batch_op:
        batch_op.drop_constraint('user_authentication_challenges_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_chart_preferences', schema=None) as batch_op:
        batch_op.drop_constraint('user_chart_preferences_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_chart_toggles', schema=None) as batch_op:
        batch_op.drop_constraint('user_chart_toggles_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_cust_sub_group_notify_service_requests_rel', schema=None) as batch_op:
        batch_op.drop_constraint('user_cust_sub_group_notify_service_reque_cust_sub_group_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('user_cust_sub_group_notify_service_requests_rel_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'cust_sub_groups', ['cust_sub_group_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_customer_rel', schema=None) as batch_op:
        batch_op.drop_constraint('user_customer_rel_customer_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('user_customer_rel_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'customers', ['customer_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_registration_challenges', schema=None) as batch_op:
        batch_op.drop_constraint('user_registration_challenges_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_role_rel', schema=None) as batch_op:
        batch_op.drop_constraint('user_role_rel_user_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('user_role_rel_role_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'roles', ['role_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_structure_maintenance_rel', schema=None) as batch_op:
        batch_op.drop_constraint('user_structure_maintenance_rel_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('user_structure_maintenance_rel_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_structure_operators_rel', schema=None) as batch_op:
        batch_op.drop_constraint('user_structure_operators_rel_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('user_structure_operators_rel_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_structure_remote_control_rel', schema=None) as batch_op:
        batch_op.drop_constraint('user_structure_remote_control_rel_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('user_structure_remote_control_rel_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_structure_sales_rel', schema=None) as batch_op:
        batch_op.drop_constraint('user_structure_sales_rel_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('user_structure_sales_rel_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_verification_codes', schema=None) as batch_op:
        batch_op.drop_constraint('user_verification_codes_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('user_webauthn_credentials', schema=None) as batch_op:
        batch_op.drop_constraint('user_webauthn_credentials_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_constraint('users_time_zone_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('users_country_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key('users_fk_customer_id', 'customers', ['customer_id'], ['id'], referent_schema='public', initially='DEFERRED', deferrable=True, use_alter=True)
        batch_op.create_foreign_key(None, 'time_zones', ['time_zone_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('warehouse_locations', schema=None) as batch_op:
        batch_op.drop_index('idx_warehouse_locations_code')
        batch_op.drop_index('idx_warehouse_locations_parent')
        batch_op.drop_index('idx_warehouse_locations_warehouse')
        batch_op.create_index(batch_op.f('ix_public_warehouse_locations_location_code'), ['location_code'], unique=False)
        batch_op.drop_constraint('warehouse_locations_parent_location_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('warehouse_locations_warehouse_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='RESTRICT')
        batch_op.create_foreign_key(None, 'warehouse_locations', ['parent_location_id'], ['id'], referent_schema='public')
        batch_op.drop_table_comment(
        existing_comment='Storage locations within warehouses (bins, shelves, zones)'
    )

    with op.batch_alter_table('warehouses', schema=None) as batch_op:
        batch_op.alter_column('time_zone_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               existing_server_default=sa.text('1'))
        batch_op.drop_index('idx_warehouses_active')
        batch_op.drop_index('idx_warehouses_country')
        batch_op.drop_index('idx_warehouses_name')
        batch_op.drop_index('idx_warehouses_province')
        batch_op.drop_constraint('warehouses_province_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'time_zones', ['time_zone_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('warehouses_parts_rel', schema=None) as batch_op:
        batch_op.alter_column('quantity_desired',
               existing_type=sa.NUMERIC(),
               nullable=False,
               existing_comment='Desired quantity for re-ordering',
               existing_server_default=sa.text('0'))
        batch_op.drop_index('idx_warehouse_part_location')
        batch_op.drop_index('idx_warehouse_part_reorder')
        batch_op.drop_constraint('warehouses_parts_rel_warehouse_id_part_id_key', type_='unique')
        batch_op.create_unique_constraint('uq_warehouse_part', ['warehouse_id', 'part_id'])
        batch_op.drop_constraint('warehouses_parts_rel_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('warehouses_parts_rel_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('warehouses_parts_rel_default_location_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'warehouse_locations', ['default_location_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='RESTRICT')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='RESTRICT')

    with op.batch_alter_table('website_views', schema=None) as batch_op:
        batch_op.drop_constraint('website_views_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_order_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint('work_order_model_type_rel_work_order_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'model_types', ['model_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_order_power_unit_rel', schema=None) as batch_op:
        batch_op.drop_constraint('work_order_power_unit_rel_power_unit_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_order_power_unit_rel_work_order_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'power_units', ['power_unit_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_order_service_rel', schema=None) as batch_op:
        batch_op.drop_constraint('work_order_service_rel_work_order_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_order_service_rel_service_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'service', ['service_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_order_structure_rel', schema=None) as batch_op:
        batch_op.drop_constraint('work_order_structure_rel_work_order_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_order_structure_rel_structure_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_order_unit_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint('work_order_unit_type_rel_work_order_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'unit_types', ['unit_type_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_order_upload_files', schema=None) as batch_op:
        batch_op.drop_constraint('work_order_upload_files_work_order_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_order_user_rel', schema=None) as batch_op:
        batch_op.drop_constraint('work_order_user_rel_work_order_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_order_user_sales_rel', schema=None) as batch_op:
        batch_op.drop_constraint('work_order_sales_user_rel_work_order_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_order_user_sales_rel_work_order_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_order_sales_user_rel_user_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')

    with op.batch_alter_table('work_orders', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False,
               autoincrement=True)
        batch_op.alter_column('is_paid',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('customer_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('approved_by_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('date_sent_for_approval',
               existing_type=sa.DATE(),
               comment=None,
               existing_comment='DEPRECATED',
               existing_nullable=True)
        batch_op.alter_column('invoice_approval_req',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='has invoice been approved already?',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('has_rcom',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='DEPRECATED',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('service_resolution',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='DEPRECATED',
               existing_nullable=True)
        batch_op.alter_column('sales_tax_rate',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=False)
        batch_op.alter_column('subtotal',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=False)
        batch_op.alter_column('discount_pct',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=False)
        batch_op.alter_column('sales_tax',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=False)
        batch_op.alter_column('total',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=False)
        batch_op.drop_constraint('work_orders_inventory_source_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_currency_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_county_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_province_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_fk_customer_id', type_='foreignkey')
        batch_op.drop_constraint('work_orders_city_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_service_type_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_fk_creator_id', type_='foreignkey')
        batch_op.drop_constraint('work_orders_status_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_zip_code_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'work_order_status', ['status_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['creator_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'service_types', ['service_type_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'countries', ['country_id'], ['id'], referent_schema='public', ondelete='RESTRICT')
        batch_op.create_foreign_key(None, 'users', ['requested_by_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'cities', ['city_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'provinces', ['province_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'inventory_sources', ['inventory_source_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'customers', ['creator_company_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'customers', ['customer_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'zip_codes', ['zip_code_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'currencies', ['currency_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'counties', ['county_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['approval_person_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key(None, 'users', ['approved_by_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.drop_column('charged_to')
        batch_op.drop_column('total_hours')
        batch_op.drop_column('time_end')
        batch_op.drop_column('requested_by')
        batch_op.drop_column('time_start')
        batch_op.drop_column('company_rep')
        batch_op.drop_column('company_contact_email')
        batch_op.drop_column('service_hours')
        batch_op.drop_column('company_contact')
        batch_op.drop_column('travel_time_hours')

    with op.batch_alter_table('work_orders_parts', schema=None) as batch_op:
        batch_op.alter_column('timestamp_utc_inserted',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DATE(),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
        batch_op.alter_column('part_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('description',
               existing_type=sa.TEXT(),
               nullable=False)
        batch_op.alter_column('warehouse_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Warehouse where parts are sourced from',
               existing_nullable=True)
        batch_op.alter_column('price',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=False)
        batch_op.alter_column('sales_tax_rate',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=False)
        batch_op.drop_constraint('work_orders_parts_structure_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_parts_work_order_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_parts_warehouse_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_parts_part_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('work_orders_parts_fk_work_order_id', type_='foreignkey')
        batch_op.drop_constraint('work_orders_parts_warehouse_to_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'parts', ['part_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'structures', ['structure_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'warehouses', ['warehouse_to_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'users', ['field_tech_id'], ['id'], referent_schema='public', ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'work_orders', ['work_order_id'], ['id'], referent_schema='public', ondelete='CASCADE')
        batch_op.drop_column('pst_part_amount')
        batch_op.drop_column('pst_rate')
        batch_op.drop_column('gst_part_amount')
        batch_op.drop_column('gst_rate')

    with op.batch_alter_table('zip_code_sales_tax', schema=None) as batch_op:
        batch_op.drop_constraint('zip_code_sales_tax_zip_code_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('zip_code_sales_tax_city_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('zip_code_sales_tax_county_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('zip_code_sales_tax_state_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key('zip_code_sales_tax_county_id_fkey', 'counties', ['county_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key('zip_code_sales_tax_city_id_fkey', 'cities', ['city_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key('zip_code_sales_tax_state_id_fkey', 'provinces', ['state_id'], ['id'], referent_schema='public')
        batch_op.create_foreign_key('zip_code_sales_tax_zip_code_id_fkey', 'zip_codes', ['zip_code_id'], ['id'], referent_schema='public')

    with op.batch_alter_table('zip_codes', schema=None) as batch_op:
        batch_op.drop_constraint('zip_codes_city_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'cities', ['city_id'], ['id'], referent_schema='public')

    op.update_view('vw_work_order_parts_joined', "SELECT t1.id AS work_order_part_id, date_part('year'::text, t2.date_service) AS year, date_part('month'::text, t2.date_service) AS month, t2.date_service, t1.work_order_id, str.structure_str AS structure, str.location, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, pu.power_unit_str AS power_unit, gw.aws_thing AS gateway, t2.timestamp_utc_inserted, t1.part_id, t3.part_num, t3.worksheet AS bom_worksheet, t3.ws_row AS worksheet_row, t3.is_usd AS is_part_usd, t3.cad_per_usd, t3.is_soft_part, t3.msrp_cad, t3.msrp_usd, t3.dealer_cost_cad, t3.dealer_cost_usd, t3.ijack_corp_cost, t3.msrp_mult_cad, t3.msrp_mult_usd, t3.transfer_mult_cad_dealer, t3.transfer_mult_usd_dealer, t3.transfer_mult_inc_to_corp, CASE WHEN t3.cost_cad IS NULL THEN t1.price::double precision / t3.msrp_mult_cad ELSE t3.cost_cad::double precision END AS cost_cad, CASE WHEN t3.cost_usd IS NULL THEN t1.price::double precision / t3.msrp_mult_usd ELSE t3.cost_usd::double precision END AS cost_usd, t1.description, t1.price, t1.quantity, t1.cost_before_tax, t1.sales_tax_rate, t1.sales_tax_part_amount AS sales_tax, t1.field_tech_id, (ft.first_name::text || ' '::text) || ft.last_name::text AS credited_to, t2.requested_by_id, t2.approval_person_id, t2.service_crew, t2.invoice_approval_req, t2.location AS wo_location, t2.has_rcom, t2.service_required, t2.service_resolution, t2.is_warranty, t2.is_warranty_reason, t2.picker_truck, t2.crew_truck, t2.man_lift, t2.trailer, t2.work_done, t2.customer_po, t2.cust_work_order, t2.afe, t2.invoice_summary, t4.customer, (t5.first_name::text || ' '::text) || t5.last_name::text AS creator, t10.name AS service_type, t11.name AS inventory_source, t2.creator_company_id, t6.customer AS creator_company, t2.country_id, countries.country_name AS country, t2.currency_id, t12.name AS currency, t13.name AS province, t16.name AS county, cities.name AS city, zip_codes.zip_code, t2.subtotal AS work_order_subtotal, t2.sales_tax AS sales_tax_total, t2.total AS work_order_total, t2.discount_pct, t2.subtotal_after_discount, t2.structure_slave, t14.name AS status, t2.is_paid, t2.notes, t2.safe_work_permit_num, t2.quickbooks_num, (t15.first_name::text || ' '::text) || t15.last_name::text AS approved_by, t2.date_due, t2.terms, t2.date_sent_for_approval, t3.part_num_group, CASE WHEN lower(str.status) ~~ '%void%'::text THEN true ELSE false END AS is_void, CASE WHEN t2.currency_id = 1 THEN true ELSE false END AS is_usd_work_order FROM work_orders_parts t1 LEFT JOIN structures str ON str.id = t1.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN power_units pu ON pu.id = str.power_unit_id LEFT JOIN gw gw ON gw.power_unit_id = pu.id LEFT JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN parts t3 ON t3.id = t1.part_id LEFT JOIN public.customers t4 ON t4.id = t2.customer_id LEFT JOIN public.customers t6 ON t6.id = t2.creator_company_id LEFT JOIN public.users t5 ON t5.id = t2.creator_id LEFT JOIN public.users ft ON ft.id = t1.field_tech_id LEFT JOIN service_types t10 ON t10.id = t2.service_type_id LEFT JOIN inventory_sources t11 ON t11.id = t2.inventory_source_id LEFT JOIN currencies t12 ON t12.id = t2.currency_id LEFT JOIN public.countries countries ON countries.id = t2.country_id LEFT JOIN provinces t13 ON t13.id = t2.province_id LEFT JOIN cities cities ON cities.id = t2.city_id LEFT JOIN zip_codes ON zip_codes.id = t2.zip_code_id LEFT JOIN work_order_status t14 ON t2.status_id = t14.id LEFT JOIN public.users t15 ON t15.id = t2.approved_by_id LEFT JOIN counties t16 ON t16.id = t2.county_id WHERE t2.is_quote = false ORDER BY t2.id DESC, t1.id", "SELECT t1.id AS work_order_part_id, date_part('year'::text, t2.date_service) AS year, date_part('month'::text, t2.date_service) AS month, t2.date_service, t1.work_order_id, str.structure_str AS structure, str.location, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, pu.power_unit_str AS power_unit, gw.aws_thing AS gateway, t2.timestamp_utc_inserted, t1.part_id, t3.part_num, t3.worksheet AS bom_worksheet, t3.ws_row AS worksheet_row, t3.is_usd AS is_part_usd, t3.cad_per_usd, t3.is_soft_part, t3.msrp_cad, t3.msrp_usd, t3.dealer_cost_cad, t3.dealer_cost_usd, t3.ijack_corp_cost, t3.msrp_mult_cad, t3.msrp_mult_usd, t3.transfer_mult_cad_dealer, t3.transfer_mult_usd_dealer, t3.transfer_mult_inc_to_corp, CASE WHEN (t3.cost_cad IS NULL) THEN ((t1.price)::double precision / t3.msrp_mult_cad) ELSE (t3.cost_cad)::double precision END AS cost_cad, CASE WHEN (t3.cost_usd IS NULL) THEN ((t1.price)::double precision / t3.msrp_mult_usd) ELSE (t3.cost_usd)::double precision END AS cost_usd, t1.description, t1.price, t1.quantity, t1.cost_before_tax, t1.sales_tax_rate, t1.sales_tax_part_amount AS sales_tax, t1.field_tech_id, (((ft.first_name)::text || ' '::text) || (ft.last_name)::text) AS credited_to, t2.requested_by_id, t2.approval_person_id, t2.service_crew, t2.invoice_approval_req, t2.location AS wo_location, t2.has_rcom, t2.service_required, t2.service_resolution, t2.is_warranty, t2.is_warranty_reason, t2.picker_truck, t2.crew_truck, t2.man_lift, t2.trailer, t2.work_done, t2.customer_po, t2.cust_work_order, t2.afe, t2.invoice_summary, t4.customer, (((t5.first_name)::text || ' '::text) || (t5.last_name)::text) AS creator, t10.name AS service_type, t11.name AS inventory_source, t2.creator_company_id, t6.customer AS creator_company, t2.country_id, countries.country_name AS country, t2.currency_id, t12.name AS currency, t13.name AS province, t16.name AS county, cities.name AS city, zip_codes.zip_code, t2.subtotal AS work_order_subtotal, t2.sales_tax AS sales_tax_total, t2.total AS work_order_total, t2.discount_pct, t2.subtotal_after_discount, t2.structure_slave, t14.name AS status, t2.is_paid, t2.notes, t2.safe_work_permit_num, t2.quickbooks_num, (((t15.first_name)::text || ' '::text) || (t15.last_name)::text) AS approved_by, t2.date_due, t2.terms, t2.date_sent_for_approval, t3.part_num_group, CASE WHEN (lower(str.status) ~~ '%%void%%'::text) THEN true ELSE false END AS is_void, CASE WHEN (t2.currency_id = 1) THEN true ELSE false END AS is_usd_work_order FROM (((((((((((((((((((((work_orders_parts t1 LEFT JOIN structures str ON ((str.id = t1.structure_id))) LEFT JOIN model_types mt ON ((mt.id = str.model_type_id))) LEFT JOIN unit_types ut ON ((ut.id = mt.unit_type_id))) LEFT JOIN power_units pu ON ((pu.id = str.power_unit_id))) LEFT JOIN gw gw ON ((gw.power_unit_id = pu.id))) LEFT JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) LEFT JOIN parts t3 ON ((t3.id = t1.part_id))) LEFT JOIN customers t4 ON ((t4.id = t2.customer_id))) LEFT JOIN customers t6 ON ((t6.id = t2.creator_company_id))) LEFT JOIN users t5 ON ((t5.id = t2.creator_id))) LEFT JOIN users ft ON ((ft.id = t1.field_tech_id))) LEFT JOIN service_types t10 ON ((t10.id = t2.service_type_id))) LEFT JOIN inventory_sources t11 ON ((t11.id = t2.inventory_source_id))) LEFT JOIN currencies t12 ON ((t12.id = t2.currency_id))) LEFT JOIN countries countries ON ((countries.id = t2.country_id))) LEFT JOIN provinces t13 ON ((t13.id = t2.province_id))) LEFT JOIN cities cities ON ((cities.id = t2.city_id))) LEFT JOIN zip_codes ON ((zip_codes.id = t2.zip_code_id))) LEFT JOIN work_order_status t14 ON ((t2.status_id = t14.id))) LEFT JOIN users t15 ON ((t15.id = t2.approved_by_id))) LEFT JOIN counties t16 ON ((t16.id = t2.county_id))) WHERE (t2.is_quote = false) ORDER BY t2.id DESC, t1.id;")
    op.update_view('vw_work_orders_by_unit', 'SELECT DISTINCT ON (wo.id, pu.id, str.id) row_number() OVER () AS id, wo.id AS work_order_id, wo.date_service, cust.customer, wo.service_required, wo.work_done, wo.is_warranty, wo.is_warranty_reason, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, put.name AS power_unit_type, str.id AS structure_id, str.structure, str.downhole, str.surface, mt.model, ut.unit_type FROM work_orders wo LEFT JOIN work_order_power_unit_rel t2 ON t2.work_order_id = wo.id LEFT JOIN power_units pu ON pu.id = t2.power_unit_id LEFT JOIN work_order_structure_rel t4 ON t4.work_order_id = wo.id LEFT JOIN structures str ON str.id = t4.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN public.power_unit_types put ON put.id = pu.power_unit_type_id LEFT JOIN work_order_user_rel wour ON wour.work_order_id = wo.id LEFT JOIN public.users users ON users.id = wour.user_id LEFT JOIN public.customers cust ON wo.customer_id = cust.id', 'SELECT DISTINCT ON (wo.id, pu.id, str.id) row_number() OVER () AS id, wo.id AS work_order_id, wo.date_service, cust.customer, wo.service_required, wo.work_done, wo.is_warranty, wo.is_warranty_reason, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, put.name AS power_unit_type, str.id AS structure_id, str.structure, str.downhole, str.surface, mt.model, ut.unit_type FROM ((((((((((work_orders wo LEFT JOIN work_order_power_unit_rel t2 ON ((t2.work_order_id = wo.id))) LEFT JOIN power_units pu ON ((pu.id = t2.power_unit_id))) LEFT JOIN work_order_structure_rel t4 ON ((t4.work_order_id = wo.id))) LEFT JOIN structures str ON ((str.id = t4.structure_id))) LEFT JOIN model_types mt ON ((mt.id = str.model_type_id))) LEFT JOIN unit_types ut ON ((ut.id = mt.unit_type_id))) LEFT JOIN power_unit_types put ON ((put.id = pu.power_unit_type_id))) LEFT JOIN work_order_user_rel wour ON ((wour.work_order_id = wo.id))) LEFT JOIN users users ON ((users.id = wour.user_id))) LEFT JOIN customers cust ON ((wo.customer_id = cust.id)));')
    op.update_view('vw_sales_by_person_year', "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)", "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN (t.user_count = 0) THEN (1)::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM (work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON ((t31_1.work_order_id = t2_1.id))) GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN (t2_1.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2_1.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1_1 JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id))) JOIN currencies fx ON ((fx.id = t2_1.currency_id))) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS name_, sum((t1.warranty_cad / (uc.user_count)::numeric)) AS sales_warranty, sum((t1.cost_before_tax_cad / (uc.user_count)::numeric)) AS sales_total, sum( CASE WHEN (t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_labour, sum( CASE WHEN (t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_parts, sum( CASE WHEN (t5.id = 1) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_new_installs, sum( CASE WHEN (t5.id = 4) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_sales, sum( CASE WHEN (t5.id = 2) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_repairs, sum( CASE WHEN (t5.id = 3) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_parts, sum( CASE WHEN (t5.id = 5) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_prevent_maint, sum( CASE WHEN (t5.id = 7) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_rentals FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) LEFT JOIN work_order_user_sales_rel t31 ON ((t31.work_order_id = t2.id))) LEFT JOIN users t3 ON ((t3.id = t31.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) JOIN user_count_per_work_order uc ON ((uc.work_order_id = t2.id))) WHERE (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY (date_part('year'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text);")
    op.update_view('vw_sales_by_person_quarter', "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('quarter'::text, t2.date_service) AS service_quarter, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('quarter'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)", "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN (t.user_count = 0) THEN (1)::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM (work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON ((t31_1.work_order_id = t2_1.id))) GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN (t2_1.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2_1.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1_1 JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id))) JOIN currencies fx ON ((fx.id = t2_1.currency_id))) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('quarter'::text, t2.date_service) AS service_quarter, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS name_, sum((t1.warranty_cad / (uc.user_count)::numeric)) AS sales_warranty, sum((t1.cost_before_tax_cad / (uc.user_count)::numeric)) AS sales_total, sum( CASE WHEN (t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_labour, sum( CASE WHEN (t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_parts, sum( CASE WHEN (t5.id = 1) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_new_installs, sum( CASE WHEN (t5.id = 4) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_sales, sum( CASE WHEN (t5.id = 2) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_repairs, sum( CASE WHEN (t5.id = 3) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_parts, sum( CASE WHEN (t5.id = 5) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_prevent_maint, sum( CASE WHEN (t5.id = 7) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_rentals FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) LEFT JOIN work_order_user_sales_rel t31 ON ((t31.work_order_id = t2.id))) LEFT JOIN users t3 ON ((t3.id = t31.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) JOIN user_count_per_work_order uc ON ((uc.work_order_id = t2.id))) WHERE (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('quarter'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text);")
    op.update_view('vw_sales_by_person_month', "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)", "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN (t.user_count = 0) THEN (1)::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM (work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON ((t31_1.work_order_id = t2_1.id))) GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN (t2_1.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2_1.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1_1 JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id))) JOIN currencies fx ON ((fx.id = t2_1.currency_id))) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS name_, sum((t1.warranty_cad / (uc.user_count)::numeric)) AS sales_warranty, sum((t1.cost_before_tax_cad / (uc.user_count)::numeric)) AS sales_total, sum( CASE WHEN (t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_labour, sum( CASE WHEN (t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_parts, sum( CASE WHEN (t5.id = 1) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_new_installs, sum( CASE WHEN (t5.id = 4) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_sales, sum( CASE WHEN (t5.id = 2) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_repairs, sum( CASE WHEN (t5.id = 3) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_parts, sum( CASE WHEN (t5.id = 5) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_prevent_maint, sum( CASE WHEN (t5.id = 7) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_rentals FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) LEFT JOIN work_order_user_sales_rel t31 ON ((t31.work_order_id = t2.id))) LEFT JOIN users t3 ON ((t3.id = t31.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) JOIN user_count_per_work_order uc ON ((uc.work_order_id = t2.id))) WHERE (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text);")
    op.update_view('vw_gw_structure_rel', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id WHERE t3.id IS NOT NULL ORDER BY t1.id', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures FROM ((gw t1 LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN structures t3 ON ((t3.power_unit_id = t2.id))) WHERE (t3.id IS NOT NULL) ORDER BY t1.id;')
    op.update_view('vw_gw_customer_rel', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id WHERE t5.id IS NOT NULL ORDER BY t1.id', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers FROM ((((gw t1 LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN structures t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t3.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) WHERE (t5.id IS NOT NULL) ORDER BY t1.id;')
    op.update_view('vw_gw_cust_sub_group_rel', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t6.id AS cust_sub_groups FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id left join public.structure_cust_sub_group_rel csr ON csr.structure_id = t3.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = csr.cust_sub_group_id WHERE t6.id IS NOT NULL ORDER BY t1.id', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t6.id AS cust_sub_groups FROM (((((gw t1 LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN structures t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t3.id))) LEFT JOIN structure_cust_sub_group_rel csr ON ((csr.structure_id = t3.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = csr.cust_sub_group_id))) WHERE (t6.id IS NOT NULL) ORDER BY t1.id;')
    op.update_view('vw_users_roles', "SELECT id, id AS user_id, name, email, customer, job_title, roles, string_agg(unit, ', '::text) AS units FROM ( SELECT a2.customer, a2.id, a2.name, a2.email, a2.job_title, a2.roles, CASE WHEN t7.power_unit_str IS NULL AND t6.surface IS NULL THEN ''::text ELSE concat(t7.power_unit_str, ' (', t6.surface, ')') END AS unit FROM ( SELECT a1.customer, a1.id, a1.name, a1.email, a1.job_title, string_agg(a1.role_name::text, ', '::text) AS roles FROM ( SELECT t2.id, t3.customer, concat(t2.first_name, ' ', t2.last_name) AS name, t2.email, t2.job_title, t4.name AS role_name FROM public.users t2 LEFT JOIN public.user_role_rel t1 ON t1.user_id = t2.id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id LEFT JOIN public.roles t4 ON t4.id = t1.role_id ORDER BY t3.customer, t2.id, t1.role_id) a1 GROUP BY a1.customer, a1.name, a1.email, a1.job_title, a1.id ORDER BY a1.customer, a1.name) a2 LEFT JOIN public.user_structure_remote_control_rel t5 ON t5.user_id = a2.id LEFT JOIN structures t6 ON t6.id = t5.structure_id LEFT JOIN power_units t7 ON t7.id = t6.power_unit_id) a3 GROUP BY customer, id, name, email, job_title, roles ORDER BY customer, id, name, email, job_title, roles", "SELECT id, id AS user_id, name, email, customer, job_title, roles, string_agg(unit, ', '::text) AS units FROM ( SELECT a2.customer, a2.id, a2.name, a2.email, a2.job_title, a2.roles, CASE WHEN ((t7.power_unit_str IS NULL) AND (t6.surface IS NULL)) THEN ''::text ELSE concat(t7.power_unit_str, ' (', t6.surface, ')') END AS unit FROM (((( SELECT a1.customer, a1.id, a1.name, a1.email, a1.job_title, string_agg((a1.role_name)::text, ', '::text) AS roles FROM ( SELECT t2.id, t3.customer, concat(t2.first_name, ' ', t2.last_name) AS name, t2.email, t2.job_title, t4.name AS role_name FROM (((users t2 LEFT JOIN user_role_rel t1 ON ((t1.user_id = t2.id))) LEFT JOIN customers t3 ON ((t3.id = t2.customer_id))) LEFT JOIN roles t4 ON ((t4.id = t1.role_id))) ORDER BY t3.customer, t2.id, t1.role_id) a1 GROUP BY a1.customer, a1.name, a1.email, a1.job_title, a1.id ORDER BY a1.customer, a1.name) a2 LEFT JOIN user_structure_remote_control_rel t5 ON ((t5.user_id = a2.id))) LEFT JOIN structures t6 ON ((t6.id = t5.structure_id))) LEFT JOIN power_units t7 ON ((t7.id = t6.power_unit_id)))) a3 GROUP BY customer, id, name, email, job_title, roles ORDER BY customer, id, name, email, job_title, roles;")
    op.update_view('gateways', "SELECT DISTINCT ON (gw.id, cust.customer) gw.id AS gateway_id, gw.gateway, gw.aws_thing, gw.mac, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, str.id AS structure_id, str.structure, str.structure_slave_id, str_sl.structure AS structure_slave, gw.ready_and_working, pu.apn, pu.alerts_edge, pu.change_detect_sens, tz.id AS time_zone_id, tz.time_zone, pu.wait_time_mins, pu.wait_time_mins_ol, pu.wait_time_mins_suction, pu.wait_time_mins_discharge, pu.wait_time_mins_spm, pu.wait_time_mins_stboxf, pu.wait_time_mins_hyd_temp, pu.hyd_oil_lvl_thresh, pu.hyd_filt_life_thresh, pu.hyd_oil_life_thresh, pu.wait_time_mins_hyd_oil_lvl, pu.wait_time_mins_hyd_filt_life, pu.wait_time_mins_hyd_oil_life, pu.wait_time_mins_chk_mtr_ovld, pu.wait_time_mins_pwr_fail, pu.wait_time_mins_soft_start_err, pu.wait_time_mins_grey_wire_err, pu.wait_time_mins_ae011, pu.heartbeat_enabled, pu.online_hb_enabled, pu.suction, pu.discharge, pu.spm, pu.stboxf, pu.hyd_temp, str.gps_lat, str.gps_lon, str.downhole, str.surface, str.location, str.well_license, cust.id AS customer_id, cust.customer, cust_sub.id AS cust_sub_group_id, cust_sub.name AS cust_sub_group, cust_sub.abbrev AS cust_sub_group_abbrev, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, mt.unit_type_id AS model_unit_type_id, ut.unit_type AS model_unit_type, str.model_type_id_slave, mt_sl.model AS model_slave, mt_sl.unit_type_id AS model_unit_type_id_slave, ut_sl.unit_type AS model_unit_type_slave, sim.sim_card, (string_to_array(cust.mqtt_topic::text, ' '::text))[1] AS mqtt_topic, str.status, CASE WHEN alerts.power_unit_id IS NULL THEN false ELSE true END AS alerts FROM gw gw LEFT JOIN power_units pu ON gw.power_unit_id = pu.id LEFT JOIN structures str ON pu.id = str.power_unit_id LEFT JOIN structures str_sl ON str.structure_slave_id = str_sl.id LEFT JOIN public.structure_customer_rel str_cust_rel ON str_cust_rel.structure_id = str.id LEFT JOIN public.customers cust ON str_cust_rel.customer_id = cust.id LEFT JOIN ( SELECT DISTINCT alerts_1.power_unit_id FROM alerts alerts_1) alerts ON pu.id = alerts.power_unit_id LEFT JOIN public.time_zones tz ON str.time_zone_id = tz.id LEFT JOIN sim_cards sim ON gw.id = sim.gateway_id LEFT JOIN public.model_types mt ON str.model_type_id = mt.id LEFT JOIN public.unit_types ut ON str.unit_type_id = ut.id LEFT JOIN public.model_types mt_sl ON str_sl.model_type_id_slave = mt_sl.id LEFT JOIN public.unit_types ut_sl ON str_sl.unit_type_id = ut_sl.id LEFT JOIN public.structure_cust_sub_group_rel csr ON csr.structure_id = str.id LEFT JOIN public.cust_sub_groups cust_sub ON cust_sub.id = csr.cust_sub_group_id ORDER BY gw.id, cust.customer", "SELECT DISTINCT ON (gw.id, cust.customer) gw.id AS gateway_id, gw.gateway, gw.aws_thing, gw.mac, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, str.id AS structure_id, str.structure, str.structure_slave_id, str_sl.structure AS structure_slave, gw.ready_and_working, pu.apn, pu.alerts_edge, pu.change_detect_sens, tz.id AS time_zone_id, tz.time_zone, pu.wait_time_mins, pu.wait_time_mins_ol, pu.wait_time_mins_suction, pu.wait_time_mins_discharge, pu.wait_time_mins_spm, pu.wait_time_mins_stboxf, pu.wait_time_mins_hyd_temp, pu.hyd_oil_lvl_thresh, pu.hyd_filt_life_thresh, pu.hyd_oil_life_thresh, pu.wait_time_mins_hyd_oil_lvl, pu.wait_time_mins_hyd_filt_life, pu.wait_time_mins_hyd_oil_life, pu.wait_time_mins_chk_mtr_ovld, pu.wait_time_mins_pwr_fail, pu.wait_time_mins_soft_start_err, pu.wait_time_mins_grey_wire_err, pu.wait_time_mins_ae011, pu.heartbeat_enabled, pu.online_hb_enabled, pu.suction, pu.discharge, pu.spm, pu.stboxf, pu.hyd_temp, str.gps_lat, str.gps_lon, str.downhole, str.surface, str.location, str.well_license, cust.id AS customer_id, cust.customer, cust_sub.id AS cust_sub_group_id, cust_sub.name AS cust_sub_group, cust_sub.abbrev AS cust_sub_group_abbrev, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, mt.unit_type_id AS model_unit_type_id, ut.unit_type AS model_unit_type, str.model_type_id_slave, mt_sl.model AS model_slave, mt_sl.unit_type_id AS model_unit_type_id_slave, ut_sl.unit_type AS model_unit_type_slave, sim.sim_card, (string_to_array((cust.mqtt_topic)::text, ' '::text))[1] AS mqtt_topic, str.status, CASE WHEN (alerts.power_unit_id IS NULL) THEN false ELSE true END AS alerts FROM ((((((((((((((gw gw LEFT JOIN power_units pu ON ((gw.power_unit_id = pu.id))) LEFT JOIN structures str ON ((pu.id = str.power_unit_id))) LEFT JOIN structures str_sl ON ((str.structure_slave_id = str_sl.id))) LEFT JOIN structure_customer_rel str_cust_rel ON ((str_cust_rel.structure_id = str.id))) LEFT JOIN customers cust ON ((str_cust_rel.customer_id = cust.id))) LEFT JOIN ( SELECT DISTINCT alerts_1.power_unit_id FROM alerts alerts_1) alerts ON ((pu.id = alerts.power_unit_id))) LEFT JOIN time_zones tz ON ((str.time_zone_id = tz.id))) LEFT JOIN sim_cards sim ON ((gw.id = sim.gateway_id))) LEFT JOIN model_types mt ON ((str.model_type_id = mt.id))) LEFT JOIN unit_types ut ON ((str.unit_type_id = ut.id))) LEFT JOIN model_types mt_sl ON ((str_sl.model_type_id_slave = mt_sl.id))) LEFT JOIN unit_types ut_sl ON ((str_sl.unit_type_id = ut_sl.id))) LEFT JOIN structure_cust_sub_group_rel csr ON ((csr.structure_id = str.id))) LEFT JOIN cust_sub_groups cust_sub ON ((cust_sub.id = csr.cust_sub_group_id))) ORDER BY gw.id, cust.customer;")
    op.update_view('vw_structures_by_model', "SELECT row_number() OVER () AS id, t2.model, max(t1.structure_install_date) AS most_recent_install_date, sum( CASE WHEN t1.model_type_id IS NOT NULL THEN 1 ELSE 0 END) AS total_units FROM structures t1 LEFT JOIN public.model_types t2 ON t1.model_type_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id WHERE (t2.model <> ALL (ARRAY['SHOP'::text, 'TEST'::text])) AND t4.customer_id IS DISTINCT FROM 21 GROUP BY t2.model ORDER BY t2.model", "SELECT row_number() OVER () AS id, t2.model, max(t1.structure_install_date) AS most_recent_install_date, sum( CASE WHEN (t1.model_type_id IS NOT NULL) THEN 1 ELSE 0 END) AS total_units FROM ((structures t1 LEFT JOIN model_types t2 ON ((t1.model_type_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) WHERE (((t2.model)::text <> ALL (ARRAY['SHOP'::text, 'TEST'::text])) AND (t4.customer_id IS DISTINCT FROM 21)) GROUP BY t2.model ORDER BY t2.model;")
    op.update_view('vw_website_most_active_users', "SELECT row_number() OVER () AS id, t1.page, count(*) AS count_, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone, min(t1.timestamp_utc) AS earliest_date_in_sample, t2.customer_id FROM public.website_views t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.user_id IS NOT NULL AND t1.page::text !~~ '%media%'::text AND t1.page::text !~~ '%protected%'::text AND (t2.customer_id <> ALL (ARRAY[1, 21])) GROUP BY t2.first_name, t2.last_name, t2.email, t2.phone, t3.customer, t2.customer_id, t1.page ORDER BY t1.page, (count(*)) DESC, t3.customer, t2.first_name, t2.last_name", "SELECT row_number() OVER () AS id, t1.page, count(*) AS count_, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone, min(t1.timestamp_utc) AS earliest_date_in_sample, t2.customer_id FROM ((website_views t1 LEFT JOIN users t2 ON ((t2.id = t1.user_id))) LEFT JOIN customers t3 ON ((t3.id = t2.customer_id))) WHERE ((t1.user_id IS NOT NULL) AND ((t1.page)::text !~~ '%%media%%'::text) AND ((t1.page)::text !~~ '%%protected%%'::text) AND (t2.customer_id <> ALL (ARRAY[1, 21]))) GROUP BY t2.first_name, t2.last_name, t2.email, t2.phone, t3.customer, t2.customer_id, t1.page ORDER BY t1.page, (count(*)) DESC, t3.customer, t2.first_name, t2.last_name;")
    op.update_view('vw_profiler', "SELECT t1.id, (t2.first_name::text || ' '::text) || t2.last_name::text AS full_name, t3.customer, t1.status_code, t1.timestamp_utc_started, t1.timestamp_utc_ended, t1.elapsed, t1.cpu_start, t1.cpu_end, t1.endpoint_name, t1.referrer, t1.method, t1.args, t1.kwargs, t1.query_string, t1.form, t1.ip, t1.files, t1.path, t1.request_args, t1.scheme, t1.user_agent, t1.body, t1.headers FROM public.profiler_measurements t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.elapsed > 3::numeric ORDER BY t1.timestamp_utc_ended DESC", "SELECT t1.id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS full_name, t3.customer, t1.status_code, t1.timestamp_utc_started, t1.timestamp_utc_ended, t1.elapsed, t1.cpu_start, t1.cpu_end, t1.endpoint_name, t1.referrer, t1.method, t1.args, t1.kwargs, t1.query_string, t1.form, t1.ip, t1.files, t1.path, t1.request_args, t1.scheme, t1.user_agent, t1.body, t1.headers FROM ((profiler_measurements t1 LEFT JOIN users t2 ON ((t2.id = t1.user_id))) LEFT JOIN customers t3 ON ((t3.id = t2.customer_id))) WHERE (t1.elapsed > (3)::numeric) ORDER BY t1.timestamp_utc_ended DESC;")
    op.update_view('vw_gw_not_connected_dont_worry_latest', 'SELECT t2.gateway_id, t2.days_since_reported, t2.timestamp_utc_last_reported, t1.timestamp_utc AS timestamp_utc_worried, t1.am_i_worried, t1.why_worried, t1.operators_contacted FROM gw_info t2 LEFT JOIN ( SELECT a1.id, a1.row_num, a1.gateway_id, a1.am_i_worried, a1.timestamp_utc, a1.why_worried, a1.operators_contacted FROM ( SELECT gw_not_connected_dont_worry.id, ( SELECT row_number() OVER (PARTITION BY gw_not_connected_dont_worry.gateway_id ORDER BY gw_not_connected_dont_worry.timestamp_utc DESC) AS row_number) AS row_num, gw_not_connected_dont_worry.gateway_id, gw_not_connected_dont_worry.am_i_worried, gw_not_connected_dont_worry.timestamp_utc, gw_not_connected_dont_worry.notes AS why_worried, gw_not_connected_dont_worry.operators_contacted FROM gw_not_connected_dont_worry) a1 WHERE a1.row_num < 2 ORDER BY a1.gateway_id) t1 ON t1.gateway_id = t2.gateway_id WHERE t2.timestamp_utc_last_reported < t1.timestamp_utc ORDER BY t2.gateway_id, t1.timestamp_utc DESC', 'SELECT t2.gateway_id, t2.days_since_reported, t2.timestamp_utc_last_reported, t1.timestamp_utc AS timestamp_utc_worried, t1.am_i_worried, t1.why_worried, t1.operators_contacted FROM (gw_info t2 LEFT JOIN ( SELECT a1.id, a1.row_num, a1.gateway_id, a1.am_i_worried, a1.timestamp_utc, a1.why_worried, a1.operators_contacted FROM ( SELECT gw_not_connected_dont_worry.id, ( SELECT row_number() OVER (PARTITION BY gw_not_connected_dont_worry.gateway_id ORDER BY gw_not_connected_dont_worry.timestamp_utc DESC) AS row_number) AS row_num, gw_not_connected_dont_worry.gateway_id, gw_not_connected_dont_worry.am_i_worried, gw_not_connected_dont_worry.timestamp_utc, gw_not_connected_dont_worry.notes AS why_worried, gw_not_connected_dont_worry.operators_contacted FROM gw_not_connected_dont_worry) a1 WHERE (a1.row_num < 2) ORDER BY a1.gateway_id) t1 ON ((t1.gateway_id = t2.gateway_id))) WHERE (t2.timestamp_utc_last_reported < t1.timestamp_utc) ORDER BY t2.gateway_id, t1.timestamp_utc DESC;')
    op.update_view('vw_structures_joined', 'SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure', 'SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN (t61.cust_sub_group_id IS NULL) THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM (((((((((((((((((((structures t1 LEFT JOIN structures t01 ON ((t01.id = t1.structure_slave_id))) LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN gw t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN structure_cust_sub_group_rel t61 ON ((t61.structure_id = t1.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = t61.cust_sub_group_id))) LEFT JOIN user_structure_remote_control_rel t7 ON ((t7.structure_id = t1.id))) LEFT JOIN users t8 ON ((t8.id = t7.user_id))) LEFT JOIN unit_types t9 ON ((t1.unit_type_id = t9.id))) LEFT JOIN model_types t10 ON ((t1.model_type_id = t10.id))) LEFT JOIN unit_types t100 ON ((t100.id = t10.unit_type_id))) LEFT JOIN model_types t11 ON ((t1.model_type_id_slave = t11.id))) LEFT JOIN unit_types t111 ON ((t111.id = t11.unit_type_id))) LEFT JOIN time_zones t12 ON ((t1.time_zone_id = t12.id))) LEFT JOIN power_unit_types t13 ON ((t13.id = t2.power_unit_type_id))) LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON ((t14.gateway_id = t3.id))) LEFT JOIN gw_info t15 ON ((t15.gateway_id = t3.id))) LEFT JOIN warehouses t16 ON ((t16.id = t1.warehouse_id))) ORDER BY t1.structure;')
    op.update_view('vw_structures_joined_filtered', 'SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure', 'SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN (t61.cust_sub_group_id IS NULL) THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM (((((((((((((((((((structures t1 LEFT JOIN structures t01 ON ((t01.id = t1.structure_slave_id))) LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN gw t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN structure_cust_sub_group_rel t61 ON ((t61.structure_id = t1.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = t61.cust_sub_group_id))) LEFT JOIN user_structure_remote_control_rel t7 ON ((t7.structure_id = t1.id))) LEFT JOIN users t8 ON ((t8.id = t7.user_id))) LEFT JOIN unit_types t9 ON ((t1.unit_type_id = t9.id))) LEFT JOIN model_types t10 ON ((t1.model_type_id = t10.id))) LEFT JOIN unit_types t100 ON ((t100.id = t10.unit_type_id))) LEFT JOIN model_types t11 ON ((t1.model_type_id_slave = t11.id))) LEFT JOIN unit_types t111 ON ((t111.id = t11.unit_type_id))) LEFT JOIN time_zones t12 ON ((t1.time_zone_id = t12.id))) LEFT JOIN power_unit_types t13 ON ((t13.id = t2.power_unit_type_id))) LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON ((t14.gateway_id = t3.id))) LEFT JOIN gw_info t15 ON ((t15.gateway_id = t3.id))) LEFT JOIN warehouses t16 ON ((t16.id = t1.warehouse_id))) WHERE ((t4.customer_id IS DISTINCT FROM 21) AND (t1.structure_install_date IS NOT NULL) AND (t10.model IS NOT NULL) AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND (t1.surface IS NOT NULL) AND (t4.customer_id IS NOT NULL) AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND ((t1.unit_type_id = 5) OR ((t1.unit_type_id <> 5) AND (t1.time_zone_id IS NOT NULL)))) ORDER BY t1.structure;')
    op.update_view('vw_structures_all', 'SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure', 'SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN (t61.cust_sub_group_id IS NULL) THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM (((((((((((((((((((structures t1 LEFT JOIN structures t01 ON ((t01.id = t1.structure_slave_id))) LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN gw t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN structure_cust_sub_group_rel t61 ON ((t61.structure_id = t1.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = t61.cust_sub_group_id))) LEFT JOIN user_structure_remote_control_rel t7 ON ((t7.structure_id = t1.id))) LEFT JOIN users t8 ON ((t8.id = t7.user_id))) LEFT JOIN unit_types t9 ON ((t1.unit_type_id = t9.id))) LEFT JOIN model_types t10 ON ((t1.model_type_id = t10.id))) LEFT JOIN unit_types t100 ON ((t100.id = t10.unit_type_id))) LEFT JOIN model_types t11 ON ((t1.model_type_id_slave = t11.id))) LEFT JOIN unit_types t111 ON ((t111.id = t11.unit_type_id))) LEFT JOIN time_zones t12 ON ((t1.time_zone_id = t12.id))) LEFT JOIN power_unit_types t13 ON ((t13.id = t2.power_unit_type_id))) LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON ((t14.gateway_id = t3.id))) LEFT JOIN gw_info t15 ON ((t15.gateway_id = t3.id))) LEFT JOIN warehouses t16 ON ((t16.id = t1.warehouse_id))) ORDER BY t1.structure;')
    op.update_view('vw_structures_all_filtered', 'SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure', 'SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN (t61.cust_sub_group_id IS NULL) THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM (((((((((((((((((((structures t1 LEFT JOIN structures t01 ON ((t01.id = t1.structure_slave_id))) LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN gw t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN structure_cust_sub_group_rel t61 ON ((t61.structure_id = t1.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = t61.cust_sub_group_id))) LEFT JOIN user_structure_remote_control_rel t7 ON ((t7.structure_id = t1.id))) LEFT JOIN users t8 ON ((t8.id = t7.user_id))) LEFT JOIN unit_types t9 ON ((t1.unit_type_id = t9.id))) LEFT JOIN model_types t10 ON ((t1.model_type_id = t10.id))) LEFT JOIN unit_types t100 ON ((t100.id = t10.unit_type_id))) LEFT JOIN model_types t11 ON ((t1.model_type_id_slave = t11.id))) LEFT JOIN unit_types t111 ON ((t111.id = t11.unit_type_id))) LEFT JOIN time_zones t12 ON ((t1.time_zone_id = t12.id))) LEFT JOIN power_unit_types t13 ON ((t13.id = t2.power_unit_type_id))) LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON ((t14.gateway_id = t3.id))) LEFT JOIN gw_info t15 ON ((t15.gateway_id = t3.id))) LEFT JOIN warehouses t16 ON ((t16.id = t1.warehouse_id))) WHERE ((t4.customer_id IS DISTINCT FROM 21) AND (t1.structure_install_date IS NOT NULL) AND (t10.model IS NOT NULL) AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND (t1.surface IS NOT NULL) AND (t4.customer_id IS NOT NULL) AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND ((t1.unit_type_id = 5) OR ((t1.unit_type_id <> 5) AND (t1.time_zone_id IS NOT NULL)))) ORDER BY t1.structure;')
    op.update_view('vw_hours_billed_by_field_tech_by_work_order', "WITH work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id AS user_id, sum(t1_1.quantity) AS quantity_billed, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id WHERE (t1_1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2_1.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_month, t1_1.user_id, (t2_1.first_name::text || ' '::text) || t2_1.last_name::text AS name_, sum(EXTRACT(epoch FROM t1_1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1_1 JOIN public.users t2_1 ON t2_1.id = t1_1.user_id WHERE t1_1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), t1_1.user_id, t2_1.first_name, t2_1.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, t1_1.user_id, t2_1.first_name, t2_1.last_name ) SELECT t2.id AS work_order_id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, t2.is_warranty, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, t1.user_id, t2.date_service, cust.customer, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed, sum(t1.warranty_cad) AS sales_warranty, sum(t1.cost_before_tax_cad) AS sales_total, sum( CASE WHEN t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t1.part_id <> ALL (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers cust ON cust.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t2.id, t2.date_service, t2.is_warranty, cust.customer, t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, t2.is_warranty, ((t3.first_name::text || ' '::text) || t3.last_name::text), t2.date_service DESC", "WITH work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id AS user_id, sum(t1_1.quantity) AS quantity_billed, sum( CASE WHEN (t2_1.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2_1.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1_1 JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id))) JOIN currencies fx ON ((fx.id = t2_1.currency_id))) WHERE ((t1_1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2_1.customer_id <> ALL (ARRAY[1, 3]))) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_month, t1_1.user_id, (((t2_1.first_name)::text || ' '::text) || (t2_1.last_name)::text) AS name_, sum((EXTRACT(epoch FROM t1_1.total_hours_worked) / (3600)::numeric)) AS hours_worked_clock FROM (service_clock t1_1 JOIN users t2_1 ON ((t2_1.id = t1_1.user_id))) WHERE (t1_1.total_hours_worked IS NOT NULL) GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), t1_1.user_id, t2_1.first_name, t2_1.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, t1_1.user_id, t2_1.first_name, t2_1.last_name ) SELECT t2.id AS work_order_id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, t2.is_warranty, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS name_, t1.user_id, t2.date_service, cust.customer, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed, sum(t1.warranty_cad) AS sales_warranty, sum(t1.cost_before_tax_cad) AS sales_total, sum( CASE WHEN (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS sales_labour, sum( CASE WHEN (t1.part_id <> ALL (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS sales_parts, sum( CASE WHEN (t5.id = 1) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_new_installs, sum( CASE WHEN (t5.id = 4) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_sales, sum( CASE WHEN (t5.id = 2) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_repairs, sum( CASE WHEN (t5.id = 3) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_parts, sum( CASE WHEN (t5.id = 5) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_prevent_maint, sum( CASE WHEN (t5.id = 7) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_rentals FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN customers cust ON ((cust.id = t2.customer_id))) JOIN users t3 ON ((t3.id = t1.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) LEFT JOIN service_clock_hours_by_year_month_user_id sc ON (((sc.service_year = date_part('year'::text, t2.date_service)) AND (sc.service_month = date_part('month'::text, t2.date_service)) AND (sc.user_id = t1.user_id)))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))) GROUP BY t2.id, t2.date_service, t2.is_warranty, cust.customer, t1.user_id, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, t2.is_warranty, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text), t2.date_service DESC;")
    op.update_view('vw_hours_billed_by_field_tech_monthly_efficiency', "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id AS user_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, t1.user_id, t2.first_name, t2.last_name ) SELECT row_number() OVER (ORDER BY service_year, service_month, full_name, user_id) AS id, service_year, service_month, full_name, user_id, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS full_name, t1.user_id, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC, full_name", "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id AS user_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN (t2.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN currencies fx ON ((fx.id = t2.currency_id))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, t1.user_id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS name_, sum((EXTRACT(epoch FROM t1.total_hours_worked) / (3600)::numeric)) AS hours_worked_clock FROM (service_clock t1 JOIN users t2 ON ((t2.id = t1.user_id))) WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, t1.user_id, t2.first_name, t2.last_name ) SELECT row_number() OVER (ORDER BY service_year, service_month, full_name, user_id) AS id, service_year, service_month, full_name, user_id, CASE WHEN (monthly_hours_worked_clock = (0)::numeric) THEN (0)::numeric ELSE (quantity_hours_billed / monthly_hours_worked_clock) END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS full_name, t1.user_id, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN customers t7 ON ((t7.id = t2.customer_id))) JOIN users t3 ON ((t3.id = t1.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) LEFT JOIN service_clock_hours_by_year_month_user_id sc ON (((sc.service_year = date_part('year'::text, t2.date_service)) AND (sc.service_month = date_part('month'::text, t2.date_service)) AND (sc.user_id = t1.user_id)))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> 1)) GROUP BY t1.user_id, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC, full_name;")
    op.update_view('vw_hours_billed_monthly_efficiency', "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC ) SELECT row_number() OVER (ORDER BY service_year, service_month) AS id, service_year, service_month, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC", "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN (t2.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN currencies fx ON ((fx.id = t2.currency_id))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, sum((EXTRACT(epoch FROM t1.total_hours_worked) / (3600)::numeric)) AS hours_worked_clock FROM service_clock t1 WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC ) SELECT row_number() OVER (ORDER BY service_year, service_month) AS id, service_year, service_month, CASE WHEN (monthly_hours_worked_clock = (0)::numeric) THEN (0)::numeric ELSE (quantity_hours_billed / monthly_hours_worked_clock) END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM (((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN customers t7 ON ((t7.id = t2.customer_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) LEFT JOIN service_clock_hours_by_year_month_user_id sc ON (((sc.service_year = date_part('year'::text, t2.date_service)) AND (sc.service_month = date_part('month'::text, t2.date_service))))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> 1)) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC;")
    op.update_view('vw_service_clock_hours_daily', "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.day_, a1.name_) AS id, a1.year_, a1.month_, a1.day_, a1.user_id, a1.name_, a1.days_worked, a1.time_records, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS day_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked, string_agg(concat(to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_in)), 'MM-DD HH24:MI'::text), ' - ', to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)), 'MM-DD HH24:MI'::text)), ', '::text ORDER BY t1.timestamp_utc_out) AS time_records FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, date_part('day'::text, work_orders.date_service) AS day_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service)), (date_part('day'::text, work_orders.date_service)), (work_orders.date_service::timestamp without time zone)) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.day_ = a2.day_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.day_, a1.user_id", "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.day_, a1.name_) AS id, a1.year_, a1.month_, a1.day_, a1.user_id, a1.name_, a1.days_worked, a1.time_records, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM (( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS day_, t1.user_id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS name_, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (3600)::double precision) AS hours_worked, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (86400)::double precision) AS days_worked, string_agg(concat(to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_in)), 'MM-DD HH24:MI'::text), ' - ', to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)), 'MM-DD HH24:MI'::text)), ', '::text ORDER BY t1.timestamp_utc_out) AS time_records FROM (service_clock t1 JOIN users t2 ON ((t2.id = t1.user_id))) WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, date_part('day'::text, work_orders.date_service) AS day_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN (t2.user_id IS NULL) THEN t1.creator_id ELSE t2.user_id END AS user_id, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS travel_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS service_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS total_hours FROM ((work_orders t1 LEFT JOIN work_order_user_rel t2 ON ((t1.id = t2.work_order_id))) LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON ((t1.id = t3.work_order_id)))) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service)), (date_part('day'::text, work_orders.date_service)), ((work_orders.date_service)::timestamp without time zone)) a2 ON (((a1.year_ = a2.year_) AND (a1.month_ = a2.month_) AND (a1.day_ = a2.day_) AND (a1.user_id = a2.user_id)))) ORDER BY a1.year_, a1.month_, a1.day_, a1.user_id;")
    op.update_view('vw_service_clock_hours_monthly', "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.name_) AS id, a1.year_, a1.month_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.user_id", "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.name_) AS id, a1.year_, a1.month_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM (( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, t1.user_id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS name_, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (3600)::double precision) AS hours_worked, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (86400)::double precision) AS days_worked FROM (service_clock t1 JOIN users t2 ON ((t2.id = t1.user_id))) WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN (t2.user_id IS NULL) THEN t1.creator_id ELSE t2.user_id END AS user_id, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS travel_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS service_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS total_hours FROM ((work_orders t1 LEFT JOIN work_order_user_rel t2 ON ((t1.id = t2.work_order_id))) LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON ((t1.id = t3.work_order_id)))) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service))) a2 ON (((a1.year_ = a2.year_) AND (a1.month_ = a2.month_) AND (a1.user_id = a2.user_id)))) ORDER BY a1.year_, a1.month_, a1.user_id;")
    op.update_view('vw_service_clock_hours_yearly', "SELECT row_number() OVER (ORDER BY a1.year_, a1.name_) AS id, a1.year_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.user_id", "SELECT row_number() OVER (ORDER BY a1.year_, a1.name_) AS id, a1.year_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM (( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, t1.user_id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS name_, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (3600)::double precision) AS hours_worked, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (86400)::double precision) AS days_worked FROM (service_clock t1 JOIN users t2 ON ((t2.id = t1.user_id))) WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN (t2.user_id IS NULL) THEN t1.creator_id ELSE t2.user_id END AS user_id, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS travel_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS service_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS total_hours FROM ((work_orders t1 LEFT JOIN work_order_user_rel t2 ON ((t1.id = t2.work_order_id))) LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON ((t1.id = t3.work_order_id)))) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service))) a2 ON (((a1.year_ = a2.year_) AND (a1.user_id = a2.user_id)))) ORDER BY a1.year_, a1.user_id;")
    op.update_view('vw_power_units_joined', '', 'SELECT t1.run_mfg_date, t1.power, t1.voltage, t1.notes, t1.id, t1.power_unit, t1.power_unit_type_id, t2.name AS power_unit_type, t2.description AS power_unit_type_desc FROM (power_units t1 LEFT JOIN myijack.power_unit_types t2 ON ((t1.power_unit_type_id = t2.id)));')
    op.update_view('vw_alerts_sent_maint_users_joined', '', "SELECT row_number() OVER () AS id, t4.timestamp_utc, t1.alerts_sent_maint_id, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone FROM (((alerts_sent_maint_users t1 LEFT JOIN myijack.users t2 ON ((t2.id = t1.user_id))) LEFT JOIN myijack.customers t3 ON ((t3.id = t2.customer_id))) LEFT JOIN alerts_sent_maint t4 ON ((t4.id = t1.alerts_sent_maint_id))) WHERE ((t1.dev_test_prd = 'production'::bpchar) AND (t2.customer_id <> 1)) ORDER BY t1.alerts_sent_maint_id DESC, t2.first_name;")
    op.update_view('vw_gw_info_plus', '', "SELECT t1.gateway_id, t1.aws_thing, t4.power_unit_str AS power_unit, t7.customer, t5.surface, t3.name AS gateway_type, t1.gw_type_reported, t1.os_name, t1.os_release, t1.os_pretty_name, t1.swv_canpy, CASE WHEN (((t1.swv_canpy)::real >= (3.0279)::double precision) AND ((t1.swv_canpy)::real < (3.0287)::double precision) AND (t3.name = 'Axiomtek'::text)) THEN 'DANGER!'::text WHEN (((t1.swv_canpy)::real >= (3.0279)::double precision) AND ((t1.swv_canpy)::real < (3.0287)::double precision) AND (t3.name = 'FATBOX'::text)) THEN 'WARNING...'::text WHEN (((t1.swv_canpy)::real >= (3.0279)::double precision) AND ((t1.swv_canpy)::real < (3.0287)::double precision) AND (t1.days_since_reported > (1)::double precision)) THEN 'WHY?'::text ELSE ''::text END AS swv_good, t1.swv_plc, t1.days_since_reported, t1.time_since_reported, t1.os_version, t1.os_version_id, t1.os_machine, t1.os_platform, t1.os_python_version, t1.modem_model, t1.modem_firmware_rev, t1.modem_drivers, t1.sim_operator, t1.timestamp_utc_updated FROM ((((((gw_info t1 LEFT JOIN gw t2 ON ((t1.gateway_id = t2.id))) LEFT JOIN gateway_types t3 ON ((t3.id = t2.gateway_type_id))) LEFT JOIN power_units t4 ON ((t4.id = t2.power_unit_id))) LEFT JOIN structures t5 ON ((t5.power_unit_id = t4.id))) LEFT JOIN myijack.structure_customer_rel t6 ON ((t6.structure_id = t5.id))) LEFT JOIN myijack.customers t7 ON ((t7.id = t6.customer_id))) WHERE ((t1.aws_thing)::text <> 'lambda_access'::text) ORDER BY t3.name, t1.os_release, t1.days_since_reported;")
    op.update_view('vw_tundra_units_w_dgas', '', 'SELECT t1.structure_install_date AS "Install Date", t6.model AS "Model", t7.name AS "Power Unit Type", t1.structure AS "Structure", t5.power_unit AS "Power Unit", t2.gateway AS "RCOM", t1.well_license AS "Well License", t1.tundra_name AS "Tundra Name", t1.dgas_pumpjack AS "DGAS Pumpjack", t1.downhole AS "Downhole", t1.surface AS "Surface" FROM (((((((structures t1 LEFT JOIN gw t2 ON ((t1.power_unit_id = t2.power_unit_id))) LEFT JOIN power_units t5 ON ((t5.id = t2.power_unit_id))) LEFT JOIN myijack.model_types t6 ON ((t6.id = t1.model_type_id))) LEFT JOIN myijack.power_unit_types t7 ON ((t7.id = t5.power_unit_type_id))) LEFT JOIN myijack.unit_types t8 ON ((t8.id = t1.unit_type_id))) LEFT JOIN myijack.structure_customer_rel t9 ON ((t9.structure_id = t1.id))) LEFT JOIN myijack.customers t10 ON ((t10.id = t9.customer_id))) WHERE ((lower((t10.customer)::text) ~~ \'tundra%\'::text) AND (t10.id = 19)) ORDER BY t8.unit_type, t1.structure, t5.power_unit, t1.structure_install_date;')
    op.update_view('vw_joins', '', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers, t6.id AS cust_sub_groups, t8.id AS users FROM ((((((((gw t1 LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN structures t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN myijack.structure_customer_rel t4 ON ((t4.structure_id = t3.id))) LEFT JOIN myijack.customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN myijack.structure_cust_sub_group_rel csr ON ((csr.structure_id = t3.id))) LEFT JOIN myijack.cust_sub_groups t6 ON ((t6.id = csr.cust_sub_group_id))) LEFT JOIN myijack.user_structure_remote_control_rel t7 ON ((t7.structure_id = t3.id))) LEFT JOIN myijack.users t8 ON ((t8.id = t7.user_id))) WHERE (t1.power_unit_id IS NOT NULL) ORDER BY t1.id;')
    op.update_view('pg_stat_statements', '', 'SELECT userid, dbid, toplevel, queryid, query, plans, total_plan_time, min_plan_time, max_plan_time, mean_plan_time, stddev_plan_time, calls, total_exec_time, min_exec_time, max_exec_time, mean_exec_time, stddev_exec_time, rows, shared_blks_hit, shared_blks_read, shared_blks_dirtied, shared_blks_written, local_blks_hit, local_blks_read, local_blks_dirtied, local_blks_written, temp_blks_read, temp_blks_written, blk_read_time, blk_write_time, temp_blk_read_time, temp_blk_write_time, wal_records, wal_fpi, wal_bytes, jit_functions, jit_generation_time, jit_inlining_count, jit_inlining_time, jit_optimization_count, jit_optimization_time, jit_emission_count, jit_emission_time FROM pg_stat_statements(true) pg_stat_statements(userid, dbid, toplevel, queryid, query, plans, total_plan_time, min_plan_time, max_plan_time, mean_plan_time, stddev_plan_time, calls, total_exec_time, min_exec_time, max_exec_time, mean_exec_time, stddev_exec_time, rows, shared_blks_hit, shared_blks_read, shared_blks_dirtied, shared_blks_written, local_blks_hit, local_blks_read, local_blks_dirtied, local_blks_written, temp_blks_read, temp_blks_written, blk_read_time, blk_write_time, temp_blk_read_time, temp_blk_write_time, wal_records, wal_fpi, wal_bytes, jit_functions, jit_generation_time, jit_inlining_count, jit_inlining_time, jit_optimization_count, jit_optimization_time, jit_emission_count, jit_emission_time);')
    op.update_view('vw_work_order_most_recent', '', 'SELECT t5.customer, t5.id AS customer_id, t8.unit_type, t7.unit_type_id, t7.model, t2.model_type_id, t10.name AS power_unit_type, t10.id AS power_unit_type_id, t9.power_unit, t9.id AS power_unit_id, t2.structure, t2.id AS structure_id, t2.surface, a1.date_service AS date_service_most_recent, a1.work_order_id FROM ((((((((structures t2 LEFT JOIN ( SELECT row_number() OVER (PARTITION BY t1.id ORDER BY t2_1.date_service DESC) AS row_num, t1.id AS structure_id, t1_1.work_order_id, t2_1.date_service FROM ((structures t1 LEFT JOIN work_order_structure_rel t1_1 ON ((t1_1.structure_id = t1.id))) LEFT JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id)))) a1 ON ((a1.structure_id = t2.id))) LEFT JOIN work_orders t3 ON ((t3.id = a1.work_order_id))) LEFT JOIN myijack.structure_customer_rel t4 ON ((t4.structure_id = t2.id))) LEFT JOIN myijack.customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN myijack.model_types t7 ON ((t7.id = t2.model_type_id))) LEFT JOIN myijack.unit_types t8 ON ((t8.id = t7.unit_type_id))) LEFT JOIN power_units t9 ON ((t9.id = t2.power_unit_id))) LEFT JOIN myijack.power_unit_types t10 ON ((t10.id = t9.power_unit_type_id))) WHERE ((a1.row_num < 2) AND (t4.customer_id IS DISTINCT FROM 21)) ORDER BY t5.customer, t8.unit_type, t7.model, t10.name, t9.power_unit, t2.structure;')
    op.update_view('pg_stat_statements_info', '', 'SELECT dealloc, stats_reset FROM pg_stat_statements_info() pg_stat_statements_info(dealloc, stats_reset);')
    op.update_view('vw_gateways_tundra', '', 'SELECT t1.gateway AS "Gateway", t6.model AS "IJACK Model", t3.well_license AS "Well License", CASE WHEN ((t3.downhole IS NOT NULL) AND (t3.downhole <> \'\'::text)) THEN concat(t3.downhole, \' @ \', t3.surface) ELSE t3.surface END AS "Location", t3.area AS "Area", t3.structure_install_date AS "Installed", t3.status AS "Status", t2.sim_card AS "SIM Card", (t2.sim_card_num)::integer AS "SIM Alias", t2.sim_card_activated AS "SIM Activated", t2.sim_card_phone AS "SIM Phone", t5.power_unit_type_id AS "Power Unit", t3.tundra_name AS "Meter Name", t3.afe AS "AFE" FROM ((((((gw t1 FULL JOIN sim_cards t2 ON ((t1.id = t2.gateway_id))) FULL JOIN structures t3 ON ((t1.power_unit_id = t3.power_unit_id))) FULL JOIN myijack.structure_customer_rel t41 ON ((t41.structure_id = t3.id))) FULL JOIN myijack.customers t4 ON ((t41.customer_id = t4.id))) LEFT JOIN power_units t5 ON ((t5.id = t3.power_unit_id))) LEFT JOIN myijack.model_types t6 ON ((t6.id = t3.model_type_id))) WHERE ((lower((t4.customer)::text) ~~ \'tundra%\'::text) AND (t4.id = 19)) ORDER BY t6.model, t3.structure;')
    # ### end Alembic commands ###


def downgrade_ijack():
    # ### commands auto generated by Alembic - please adjust! ###
    op.update_view('vw_gateways_tundra', 'SELECT t1.gateway AS "Gateway", t6.model AS "IJACK Model", t3.well_license AS "Well License", CASE WHEN ((t3.downhole IS NOT NULL) AND (t3.downhole <> \'\'::text)) THEN concat(t3.downhole, \' @ \', t3.surface) ELSE t3.surface END AS "Location", t3.area AS "Area", t3.structure_install_date AS "Installed", t3.status AS "Status", t2.sim_card AS "SIM Card", (t2.sim_card_num)::integer AS "SIM Alias", t2.sim_card_activated AS "SIM Activated", t2.sim_card_phone AS "SIM Phone", t5.power_unit_type_id AS "Power Unit", t3.tundra_name AS "Meter Name", t3.afe AS "AFE" FROM ((((((gw t1 FULL JOIN sim_cards t2 ON ((t1.id = t2.gateway_id))) FULL JOIN structures t3 ON ((t1.power_unit_id = t3.power_unit_id))) FULL JOIN myijack.structure_customer_rel t41 ON ((t41.structure_id = t3.id))) FULL JOIN myijack.customers t4 ON ((t41.customer_id = t4.id))) LEFT JOIN power_units t5 ON ((t5.id = t3.power_unit_id))) LEFT JOIN myijack.model_types t6 ON ((t6.id = t3.model_type_id))) WHERE ((lower((t4.customer)::text) ~~ \'tundra%\'::text) AND (t4.id = 19)) ORDER BY t6.model, t3.structure;', '')
    op.update_view('pg_stat_statements_info', 'SELECT dealloc, stats_reset FROM pg_stat_statements_info() pg_stat_statements_info(dealloc, stats_reset);', '')
    op.update_view('vw_work_order_most_recent', 'SELECT t5.customer, t5.id AS customer_id, t8.unit_type, t7.unit_type_id, t7.model, t2.model_type_id, t10.name AS power_unit_type, t10.id AS power_unit_type_id, t9.power_unit, t9.id AS power_unit_id, t2.structure, t2.id AS structure_id, t2.surface, a1.date_service AS date_service_most_recent, a1.work_order_id FROM ((((((((structures t2 LEFT JOIN ( SELECT row_number() OVER (PARTITION BY t1.id ORDER BY t2_1.date_service DESC) AS row_num, t1.id AS structure_id, t1_1.work_order_id, t2_1.date_service FROM ((structures t1 LEFT JOIN work_order_structure_rel t1_1 ON ((t1_1.structure_id = t1.id))) LEFT JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id)))) a1 ON ((a1.structure_id = t2.id))) LEFT JOIN work_orders t3 ON ((t3.id = a1.work_order_id))) LEFT JOIN myijack.structure_customer_rel t4 ON ((t4.structure_id = t2.id))) LEFT JOIN myijack.customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN myijack.model_types t7 ON ((t7.id = t2.model_type_id))) LEFT JOIN myijack.unit_types t8 ON ((t8.id = t7.unit_type_id))) LEFT JOIN power_units t9 ON ((t9.id = t2.power_unit_id))) LEFT JOIN myijack.power_unit_types t10 ON ((t10.id = t9.power_unit_type_id))) WHERE ((a1.row_num < 2) AND (t4.customer_id IS DISTINCT FROM 21)) ORDER BY t5.customer, t8.unit_type, t7.model, t10.name, t9.power_unit, t2.structure;', '')
    op.update_view('pg_stat_statements', 'SELECT userid, dbid, toplevel, queryid, query, plans, total_plan_time, min_plan_time, max_plan_time, mean_plan_time, stddev_plan_time, calls, total_exec_time, min_exec_time, max_exec_time, mean_exec_time, stddev_exec_time, rows, shared_blks_hit, shared_blks_read, shared_blks_dirtied, shared_blks_written, local_blks_hit, local_blks_read, local_blks_dirtied, local_blks_written, temp_blks_read, temp_blks_written, blk_read_time, blk_write_time, temp_blk_read_time, temp_blk_write_time, wal_records, wal_fpi, wal_bytes, jit_functions, jit_generation_time, jit_inlining_count, jit_inlining_time, jit_optimization_count, jit_optimization_time, jit_emission_count, jit_emission_time FROM pg_stat_statements(true) pg_stat_statements(userid, dbid, toplevel, queryid, query, plans, total_plan_time, min_plan_time, max_plan_time, mean_plan_time, stddev_plan_time, calls, total_exec_time, min_exec_time, max_exec_time, mean_exec_time, stddev_exec_time, rows, shared_blks_hit, shared_blks_read, shared_blks_dirtied, shared_blks_written, local_blks_hit, local_blks_read, local_blks_dirtied, local_blks_written, temp_blks_read, temp_blks_written, blk_read_time, blk_write_time, temp_blk_read_time, temp_blk_write_time, wal_records, wal_fpi, wal_bytes, jit_functions, jit_generation_time, jit_inlining_count, jit_inlining_time, jit_optimization_count, jit_optimization_time, jit_emission_count, jit_emission_time);', '')
    op.update_view('vw_joins', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers, t6.id AS cust_sub_groups, t8.id AS users FROM ((((((((gw t1 LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN structures t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN myijack.structure_customer_rel t4 ON ((t4.structure_id = t3.id))) LEFT JOIN myijack.customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN myijack.structure_cust_sub_group_rel csr ON ((csr.structure_id = t3.id))) LEFT JOIN myijack.cust_sub_groups t6 ON ((t6.id = csr.cust_sub_group_id))) LEFT JOIN myijack.user_structure_remote_control_rel t7 ON ((t7.structure_id = t3.id))) LEFT JOIN myijack.users t8 ON ((t8.id = t7.user_id))) WHERE (t1.power_unit_id IS NOT NULL) ORDER BY t1.id;', '')
    op.update_view('vw_tundra_units_w_dgas', 'SELECT t1.structure_install_date AS "Install Date", t6.model AS "Model", t7.name AS "Power Unit Type", t1.structure AS "Structure", t5.power_unit AS "Power Unit", t2.gateway AS "RCOM", t1.well_license AS "Well License", t1.tundra_name AS "Tundra Name", t1.dgas_pumpjack AS "DGAS Pumpjack", t1.downhole AS "Downhole", t1.surface AS "Surface" FROM (((((((structures t1 LEFT JOIN gw t2 ON ((t1.power_unit_id = t2.power_unit_id))) LEFT JOIN power_units t5 ON ((t5.id = t2.power_unit_id))) LEFT JOIN myijack.model_types t6 ON ((t6.id = t1.model_type_id))) LEFT JOIN myijack.power_unit_types t7 ON ((t7.id = t5.power_unit_type_id))) LEFT JOIN myijack.unit_types t8 ON ((t8.id = t1.unit_type_id))) LEFT JOIN myijack.structure_customer_rel t9 ON ((t9.structure_id = t1.id))) LEFT JOIN myijack.customers t10 ON ((t10.id = t9.customer_id))) WHERE ((lower((t10.customer)::text) ~~ \'tundra%\'::text) AND (t10.id = 19)) ORDER BY t8.unit_type, t1.structure, t5.power_unit, t1.structure_install_date;', '')
    op.update_view('vw_gw_info_plus', "SELECT t1.gateway_id, t1.aws_thing, t4.power_unit_str AS power_unit, t7.customer, t5.surface, t3.name AS gateway_type, t1.gw_type_reported, t1.os_name, t1.os_release, t1.os_pretty_name, t1.swv_canpy, CASE WHEN (((t1.swv_canpy)::real >= (3.0279)::double precision) AND ((t1.swv_canpy)::real < (3.0287)::double precision) AND (t3.name = 'Axiomtek'::text)) THEN 'DANGER!'::text WHEN (((t1.swv_canpy)::real >= (3.0279)::double precision) AND ((t1.swv_canpy)::real < (3.0287)::double precision) AND (t3.name = 'FATBOX'::text)) THEN 'WARNING...'::text WHEN (((t1.swv_canpy)::real >= (3.0279)::double precision) AND ((t1.swv_canpy)::real < (3.0287)::double precision) AND (t1.days_since_reported > (1)::double precision)) THEN 'WHY?'::text ELSE ''::text END AS swv_good, t1.swv_plc, t1.days_since_reported, t1.time_since_reported, t1.os_version, t1.os_version_id, t1.os_machine, t1.os_platform, t1.os_python_version, t1.modem_model, t1.modem_firmware_rev, t1.modem_drivers, t1.sim_operator, t1.timestamp_utc_updated FROM ((((((gw_info t1 LEFT JOIN gw t2 ON ((t1.gateway_id = t2.id))) LEFT JOIN gateway_types t3 ON ((t3.id = t2.gateway_type_id))) LEFT JOIN power_units t4 ON ((t4.id = t2.power_unit_id))) LEFT JOIN structures t5 ON ((t5.power_unit_id = t4.id))) LEFT JOIN myijack.structure_customer_rel t6 ON ((t6.structure_id = t5.id))) LEFT JOIN myijack.customers t7 ON ((t7.id = t6.customer_id))) WHERE ((t1.aws_thing)::text <> 'lambda_access'::text) ORDER BY t3.name, t1.os_release, t1.days_since_reported;", '')
    op.update_view('vw_alerts_sent_maint_users_joined', "SELECT row_number() OVER () AS id, t4.timestamp_utc, t1.alerts_sent_maint_id, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone FROM (((alerts_sent_maint_users t1 LEFT JOIN myijack.users t2 ON ((t2.id = t1.user_id))) LEFT JOIN myijack.customers t3 ON ((t3.id = t2.customer_id))) LEFT JOIN alerts_sent_maint t4 ON ((t4.id = t1.alerts_sent_maint_id))) WHERE ((t1.dev_test_prd = 'production'::bpchar) AND (t2.customer_id <> 1)) ORDER BY t1.alerts_sent_maint_id DESC, t2.first_name;", '')
    op.update_view('vw_power_units_joined', 'SELECT t1.run_mfg_date, t1.power, t1.voltage, t1.notes, t1.id, t1.power_unit, t1.power_unit_type_id, t2.name AS power_unit_type, t2.description AS power_unit_type_desc FROM (power_units t1 LEFT JOIN myijack.power_unit_types t2 ON ((t1.power_unit_type_id = t2.id)));', '')
    op.update_view('vw_service_clock_hours_yearly', "SELECT row_number() OVER (ORDER BY a1.year_, a1.name_) AS id, a1.year_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM (( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, t1.user_id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS name_, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (3600)::double precision) AS hours_worked, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (86400)::double precision) AS days_worked FROM (service_clock t1 JOIN users t2 ON ((t2.id = t1.user_id))) WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN (t2.user_id IS NULL) THEN t1.creator_id ELSE t2.user_id END AS user_id, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS travel_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS service_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS total_hours FROM ((work_orders t1 LEFT JOIN work_order_user_rel t2 ON ((t1.id = t2.work_order_id))) LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON ((t1.id = t3.work_order_id)))) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service))) a2 ON (((a1.year_ = a2.year_) AND (a1.user_id = a2.user_id)))) ORDER BY a1.year_, a1.user_id;", "SELECT row_number() OVER (ORDER BY a1.year_, a1.name_) AS id, a1.year_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.user_id")
    op.update_view('vw_service_clock_hours_monthly', "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.name_) AS id, a1.year_, a1.month_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM (( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, t1.user_id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS name_, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (3600)::double precision) AS hours_worked, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (86400)::double precision) AS days_worked FROM (service_clock t1 JOIN users t2 ON ((t2.id = t1.user_id))) WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN (t2.user_id IS NULL) THEN t1.creator_id ELSE t2.user_id END AS user_id, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS travel_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS service_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS total_hours FROM ((work_orders t1 LEFT JOIN work_order_user_rel t2 ON ((t1.id = t2.work_order_id))) LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON ((t1.id = t3.work_order_id)))) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service))) a2 ON (((a1.year_ = a2.year_) AND (a1.month_ = a2.month_) AND (a1.user_id = a2.user_id)))) ORDER BY a1.year_, a1.month_, a1.user_id;", "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.name_) AS id, a1.year_, a1.month_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.user_id")
    op.update_view('vw_service_clock_hours_daily', "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.day_, a1.name_) AS id, a1.year_, a1.month_, a1.day_, a1.user_id, a1.name_, a1.days_worked, a1.time_records, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM (( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS day_, t1.user_id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS name_, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (3600)::double precision) AS hours_worked, (date_part('epoch'::text, sum(t1.total_hours_worked)) / (86400)::double precision) AS days_worked, string_agg(concat(to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_in)), 'MM-DD HH24:MI'::text), ' - ', to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)), 'MM-DD HH24:MI'::text)), ', '::text ORDER BY t1.timestamp_utc_out) AS time_records FROM (service_clock t1 JOIN users t2 ON ((t2.id = t1.user_id))) WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, date_part('day'::text, work_orders.date_service) AS day_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN (t2.user_id IS NULL) THEN t1.creator_id ELSE t2.user_id END AS user_id, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS travel_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS service_hours, ( CASE WHEN (t3.n_users IS NULL) THEN (1)::bigint ELSE t3.n_users END)::numeric AS total_hours FROM ((work_orders t1 LEFT JOIN work_order_user_rel t2 ON ((t1.id = t2.work_order_id))) LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON ((t1.id = t3.work_order_id)))) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service)), (date_part('day'::text, work_orders.date_service)), ((work_orders.date_service)::timestamp without time zone)) a2 ON (((a1.year_ = a2.year_) AND (a1.month_ = a2.month_) AND (a1.day_ = a2.day_) AND (a1.user_id = a2.user_id)))) ORDER BY a1.year_, a1.month_, a1.day_, a1.user_id;", "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.day_, a1.name_) AS id, a1.year_, a1.month_, a1.day_, a1.user_id, a1.name_, a1.days_worked, a1.time_records, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS day_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked, string_agg(concat(to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_in)), 'MM-DD HH24:MI'::text), ' - ', to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)), 'MM-DD HH24:MI'::text)), ', '::text ORDER BY t1.timestamp_utc_out) AS time_records FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, date_part('day'::text, work_orders.date_service) AS day_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service)), (date_part('day'::text, work_orders.date_service)), (work_orders.date_service::timestamp without time zone)) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.day_ = a2.day_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.day_, a1.user_id")
    op.update_view('vw_hours_billed_monthly_efficiency', "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN (t2.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN currencies fx ON ((fx.id = t2.currency_id))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, sum((EXTRACT(epoch FROM t1.total_hours_worked) / (3600)::numeric)) AS hours_worked_clock FROM service_clock t1 WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC ) SELECT row_number() OVER (ORDER BY service_year, service_month) AS id, service_year, service_month, CASE WHEN (monthly_hours_worked_clock = (0)::numeric) THEN (0)::numeric ELSE (quantity_hours_billed / monthly_hours_worked_clock) END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM (((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN customers t7 ON ((t7.id = t2.customer_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) LEFT JOIN service_clock_hours_by_year_month_user_id sc ON (((sc.service_year = date_part('year'::text, t2.date_service)) AND (sc.service_month = date_part('month'::text, t2.date_service))))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> 1)) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC;", "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC ) SELECT row_number() OVER (ORDER BY service_year, service_month) AS id, service_year, service_month, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC")
    op.update_view('vw_hours_billed_by_field_tech_monthly_efficiency', "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id AS user_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN (t2.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN currencies fx ON ((fx.id = t2.currency_id))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, t1.user_id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS name_, sum((EXTRACT(epoch FROM t1.total_hours_worked) / (3600)::numeric)) AS hours_worked_clock FROM (service_clock t1 JOIN users t2 ON ((t2.id = t1.user_id))) WHERE (t1.total_hours_worked IS NOT NULL) GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, t1.user_id, t2.first_name, t2.last_name ) SELECT row_number() OVER (ORDER BY service_year, service_month, full_name, user_id) AS id, service_year, service_month, full_name, user_id, CASE WHEN (monthly_hours_worked_clock = (0)::numeric) THEN (0)::numeric ELSE (quantity_hours_billed / monthly_hours_worked_clock) END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS full_name, t1.user_id, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN customers t7 ON ((t7.id = t2.customer_id))) JOIN users t3 ON ((t3.id = t1.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) LEFT JOIN service_clock_hours_by_year_month_user_id sc ON (((sc.service_year = date_part('year'::text, t2.date_service)) AND (sc.service_month = date_part('month'::text, t2.date_service)) AND (sc.user_id = t1.user_id)))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> 1)) GROUP BY t1.user_id, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC, full_name;", "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id AS user_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, t1.user_id, t2.first_name, t2.last_name ) SELECT row_number() OVER (ORDER BY service_year, service_month, full_name, user_id) AS id, service_year, service_month, full_name, user_id, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS full_name, t1.user_id, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC, full_name")
    op.update_view('vw_hours_billed_by_field_tech_by_work_order', "WITH work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id AS user_id, sum(t1_1.quantity) AS quantity_billed, sum( CASE WHEN (t2_1.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2_1.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1_1 JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id))) JOIN currencies fx ON ((fx.id = t2_1.currency_id))) WHERE ((t1_1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2_1.customer_id <> ALL (ARRAY[1, 3]))) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_month, t1_1.user_id, (((t2_1.first_name)::text || ' '::text) || (t2_1.last_name)::text) AS name_, sum((EXTRACT(epoch FROM t1_1.total_hours_worked) / (3600)::numeric)) AS hours_worked_clock FROM (service_clock t1_1 JOIN users t2_1 ON ((t2_1.id = t1_1.user_id))) WHERE (t1_1.total_hours_worked IS NOT NULL) GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), t1_1.user_id, t2_1.first_name, t2_1.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, t1_1.user_id, t2_1.first_name, t2_1.last_name ) SELECT t2.id AS work_order_id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, t2.is_warranty, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS name_, t1.user_id, t2.date_service, cust.customer, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed, sum(t1.warranty_cad) AS sales_warranty, sum(t1.cost_before_tax_cad) AS sales_total, sum( CASE WHEN (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS sales_labour, sum( CASE WHEN (t1.part_id <> ALL (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS sales_parts, sum( CASE WHEN (t5.id = 1) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_new_installs, sum( CASE WHEN (t5.id = 4) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_sales, sum( CASE WHEN (t5.id = 2) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_repairs, sum( CASE WHEN (t5.id = 3) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_parts, sum( CASE WHEN (t5.id = 5) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_prevent_maint, sum( CASE WHEN (t5.id = 7) THEN t1.cost_before_tax_cad ELSE (0)::numeric END) AS type_rentals FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) JOIN customers cust ON ((cust.id = t2.customer_id))) JOIN users t3 ON ((t3.id = t1.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) LEFT JOIN service_clock_hours_by_year_month_user_id sc ON (((sc.service_year = date_part('year'::text, t2.date_service)) AND (sc.service_month = date_part('month'::text, t2.date_service)) AND (sc.user_id = t1.user_id)))) WHERE ((t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))) GROUP BY t2.id, t2.date_service, t2.is_warranty, cust.customer, t1.user_id, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, t2.is_warranty, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text), t2.date_service DESC;", "WITH work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id AS user_id, sum(t1_1.quantity) AS quantity_billed, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id WHERE (t1_1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2_1.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_month, t1_1.user_id, (t2_1.first_name::text || ' '::text) || t2_1.last_name::text AS name_, sum(EXTRACT(epoch FROM t1_1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1_1 JOIN public.users t2_1 ON t2_1.id = t1_1.user_id WHERE t1_1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), t1_1.user_id, t2_1.first_name, t2_1.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, t1_1.user_id, t2_1.first_name, t2_1.last_name ) SELECT t2.id AS work_order_id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, t2.is_warranty, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, t1.user_id, t2.date_service, cust.customer, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed, sum(t1.warranty_cad) AS sales_warranty, sum(t1.cost_before_tax_cad) AS sales_total, sum( CASE WHEN t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t1.part_id <> ALL (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers cust ON cust.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t2.id, t2.date_service, t2.is_warranty, cust.customer, t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, t2.is_warranty, ((t3.first_name::text || ' '::text) || t3.last_name::text), t2.date_service DESC")
    op.update_view('vw_structures_all_filtered', 'SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN (t61.cust_sub_group_id IS NULL) THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM (((((((((((((((((((structures t1 LEFT JOIN structures t01 ON ((t01.id = t1.structure_slave_id))) LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN gw t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN structure_cust_sub_group_rel t61 ON ((t61.structure_id = t1.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = t61.cust_sub_group_id))) LEFT JOIN user_structure_remote_control_rel t7 ON ((t7.structure_id = t1.id))) LEFT JOIN users t8 ON ((t8.id = t7.user_id))) LEFT JOIN unit_types t9 ON ((t1.unit_type_id = t9.id))) LEFT JOIN model_types t10 ON ((t1.model_type_id = t10.id))) LEFT JOIN unit_types t100 ON ((t100.id = t10.unit_type_id))) LEFT JOIN model_types t11 ON ((t1.model_type_id_slave = t11.id))) LEFT JOIN unit_types t111 ON ((t111.id = t11.unit_type_id))) LEFT JOIN time_zones t12 ON ((t1.time_zone_id = t12.id))) LEFT JOIN power_unit_types t13 ON ((t13.id = t2.power_unit_type_id))) LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON ((t14.gateway_id = t3.id))) LEFT JOIN gw_info t15 ON ((t15.gateway_id = t3.id))) LEFT JOIN warehouses t16 ON ((t16.id = t1.warehouse_id))) WHERE ((t4.customer_id IS DISTINCT FROM 21) AND (t1.structure_install_date IS NOT NULL) AND (t10.model IS NOT NULL) AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND (t1.surface IS NOT NULL) AND (t4.customer_id IS NOT NULL) AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND ((t1.unit_type_id = 5) OR ((t1.unit_type_id <> 5) AND (t1.time_zone_id IS NOT NULL)))) ORDER BY t1.structure;', 'SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure')
    op.update_view('vw_structures_all', 'SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN (t61.cust_sub_group_id IS NULL) THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM (((((((((((((((((((structures t1 LEFT JOIN structures t01 ON ((t01.id = t1.structure_slave_id))) LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN gw t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN structure_cust_sub_group_rel t61 ON ((t61.structure_id = t1.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = t61.cust_sub_group_id))) LEFT JOIN user_structure_remote_control_rel t7 ON ((t7.structure_id = t1.id))) LEFT JOIN users t8 ON ((t8.id = t7.user_id))) LEFT JOIN unit_types t9 ON ((t1.unit_type_id = t9.id))) LEFT JOIN model_types t10 ON ((t1.model_type_id = t10.id))) LEFT JOIN unit_types t100 ON ((t100.id = t10.unit_type_id))) LEFT JOIN model_types t11 ON ((t1.model_type_id_slave = t11.id))) LEFT JOIN unit_types t111 ON ((t111.id = t11.unit_type_id))) LEFT JOIN time_zones t12 ON ((t1.time_zone_id = t12.id))) LEFT JOIN power_unit_types t13 ON ((t13.id = t2.power_unit_type_id))) LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON ((t14.gateway_id = t3.id))) LEFT JOIN gw_info t15 ON ((t15.gateway_id = t3.id))) LEFT JOIN warehouses t16 ON ((t16.id = t1.warehouse_id))) ORDER BY t1.structure;', 'SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure')
    op.update_view('vw_structures_joined_filtered', 'SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN (t61.cust_sub_group_id IS NULL) THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM (((((((((((((((((((structures t1 LEFT JOIN structures t01 ON ((t01.id = t1.structure_slave_id))) LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN gw t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN structure_cust_sub_group_rel t61 ON ((t61.structure_id = t1.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = t61.cust_sub_group_id))) LEFT JOIN user_structure_remote_control_rel t7 ON ((t7.structure_id = t1.id))) LEFT JOIN users t8 ON ((t8.id = t7.user_id))) LEFT JOIN unit_types t9 ON ((t1.unit_type_id = t9.id))) LEFT JOIN model_types t10 ON ((t1.model_type_id = t10.id))) LEFT JOIN unit_types t100 ON ((t100.id = t10.unit_type_id))) LEFT JOIN model_types t11 ON ((t1.model_type_id_slave = t11.id))) LEFT JOIN unit_types t111 ON ((t111.id = t11.unit_type_id))) LEFT JOIN time_zones t12 ON ((t1.time_zone_id = t12.id))) LEFT JOIN power_unit_types t13 ON ((t13.id = t2.power_unit_type_id))) LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON ((t14.gateway_id = t3.id))) LEFT JOIN gw_info t15 ON ((t15.gateway_id = t3.id))) LEFT JOIN warehouses t16 ON ((t16.id = t1.warehouse_id))) WHERE ((t4.customer_id IS DISTINCT FROM 21) AND (t1.structure_install_date IS NOT NULL) AND (t10.model IS NOT NULL) AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND (t1.surface IS NOT NULL) AND (t4.customer_id IS NOT NULL) AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND ((t1.unit_type_id = 5) OR ((t1.unit_type_id <> 5) AND (t1.time_zone_id IS NOT NULL)))) ORDER BY t1.structure;', 'SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure')
    op.update_view('vw_structures_joined', 'SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN (t61.cust_sub_group_id IS NULL) THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM (((((((((((((((((((structures t1 LEFT JOIN structures t01 ON ((t01.id = t1.structure_slave_id))) LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN gw t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) LEFT JOIN structure_cust_sub_group_rel t61 ON ((t61.structure_id = t1.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = t61.cust_sub_group_id))) LEFT JOIN user_structure_remote_control_rel t7 ON ((t7.structure_id = t1.id))) LEFT JOIN users t8 ON ((t8.id = t7.user_id))) LEFT JOIN unit_types t9 ON ((t1.unit_type_id = t9.id))) LEFT JOIN model_types t10 ON ((t1.model_type_id = t10.id))) LEFT JOIN unit_types t100 ON ((t100.id = t10.unit_type_id))) LEFT JOIN model_types t11 ON ((t1.model_type_id_slave = t11.id))) LEFT JOIN unit_types t111 ON ((t111.id = t11.unit_type_id))) LEFT JOIN time_zones t12 ON ((t1.time_zone_id = t12.id))) LEFT JOIN power_unit_types t13 ON ((t13.id = t2.power_unit_type_id))) LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON ((t14.gateway_id = t3.id))) LEFT JOIN gw_info t15 ON ((t15.gateway_id = t3.id))) LEFT JOIN warehouses t16 ON ((t16.id = t1.warehouse_id))) ORDER BY t1.structure;', 'SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure')
    op.update_view('vw_gw_not_connected_dont_worry_latest', 'SELECT t2.gateway_id, t2.days_since_reported, t2.timestamp_utc_last_reported, t1.timestamp_utc AS timestamp_utc_worried, t1.am_i_worried, t1.why_worried, t1.operators_contacted FROM (gw_info t2 LEFT JOIN ( SELECT a1.id, a1.row_num, a1.gateway_id, a1.am_i_worried, a1.timestamp_utc, a1.why_worried, a1.operators_contacted FROM ( SELECT gw_not_connected_dont_worry.id, ( SELECT row_number() OVER (PARTITION BY gw_not_connected_dont_worry.gateway_id ORDER BY gw_not_connected_dont_worry.timestamp_utc DESC) AS row_number) AS row_num, gw_not_connected_dont_worry.gateway_id, gw_not_connected_dont_worry.am_i_worried, gw_not_connected_dont_worry.timestamp_utc, gw_not_connected_dont_worry.notes AS why_worried, gw_not_connected_dont_worry.operators_contacted FROM gw_not_connected_dont_worry) a1 WHERE (a1.row_num < 2) ORDER BY a1.gateway_id) t1 ON ((t1.gateway_id = t2.gateway_id))) WHERE (t2.timestamp_utc_last_reported < t1.timestamp_utc) ORDER BY t2.gateway_id, t1.timestamp_utc DESC;', 'SELECT t2.gateway_id, t2.days_since_reported, t2.timestamp_utc_last_reported, t1.timestamp_utc AS timestamp_utc_worried, t1.am_i_worried, t1.why_worried, t1.operators_contacted FROM gw_info t2 LEFT JOIN ( SELECT a1.id, a1.row_num, a1.gateway_id, a1.am_i_worried, a1.timestamp_utc, a1.why_worried, a1.operators_contacted FROM ( SELECT gw_not_connected_dont_worry.id, ( SELECT row_number() OVER (PARTITION BY gw_not_connected_dont_worry.gateway_id ORDER BY gw_not_connected_dont_worry.timestamp_utc DESC) AS row_number) AS row_num, gw_not_connected_dont_worry.gateway_id, gw_not_connected_dont_worry.am_i_worried, gw_not_connected_dont_worry.timestamp_utc, gw_not_connected_dont_worry.notes AS why_worried, gw_not_connected_dont_worry.operators_contacted FROM gw_not_connected_dont_worry) a1 WHERE a1.row_num < 2 ORDER BY a1.gateway_id) t1 ON t1.gateway_id = t2.gateway_id WHERE t2.timestamp_utc_last_reported < t1.timestamp_utc ORDER BY t2.gateway_id, t1.timestamp_utc DESC')
    op.update_view('vw_profiler', "SELECT t1.id, (((t2.first_name)::text || ' '::text) || (t2.last_name)::text) AS full_name, t3.customer, t1.status_code, t1.timestamp_utc_started, t1.timestamp_utc_ended, t1.elapsed, t1.cpu_start, t1.cpu_end, t1.endpoint_name, t1.referrer, t1.method, t1.args, t1.kwargs, t1.query_string, t1.form, t1.ip, t1.files, t1.path, t1.request_args, t1.scheme, t1.user_agent, t1.body, t1.headers FROM ((profiler_measurements t1 LEFT JOIN users t2 ON ((t2.id = t1.user_id))) LEFT JOIN customers t3 ON ((t3.id = t2.customer_id))) WHERE (t1.elapsed > (3)::numeric) ORDER BY t1.timestamp_utc_ended DESC;", "SELECT t1.id, (t2.first_name::text || ' '::text) || t2.last_name::text AS full_name, t3.customer, t1.status_code, t1.timestamp_utc_started, t1.timestamp_utc_ended, t1.elapsed, t1.cpu_start, t1.cpu_end, t1.endpoint_name, t1.referrer, t1.method, t1.args, t1.kwargs, t1.query_string, t1.form, t1.ip, t1.files, t1.path, t1.request_args, t1.scheme, t1.user_agent, t1.body, t1.headers FROM public.profiler_measurements t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.elapsed > 3::numeric ORDER BY t1.timestamp_utc_ended DESC")
    op.update_view('vw_website_most_active_users', "SELECT row_number() OVER () AS id, t1.page, count(*) AS count_, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone, min(t1.timestamp_utc) AS earliest_date_in_sample, t2.customer_id FROM ((website_views t1 LEFT JOIN users t2 ON ((t2.id = t1.user_id))) LEFT JOIN customers t3 ON ((t3.id = t2.customer_id))) WHERE ((t1.user_id IS NOT NULL) AND ((t1.page)::text !~~ '%%media%%'::text) AND ((t1.page)::text !~~ '%%protected%%'::text) AND (t2.customer_id <> ALL (ARRAY[1, 21]))) GROUP BY t2.first_name, t2.last_name, t2.email, t2.phone, t3.customer, t2.customer_id, t1.page ORDER BY t1.page, (count(*)) DESC, t3.customer, t2.first_name, t2.last_name;", "SELECT row_number() OVER () AS id, t1.page, count(*) AS count_, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone, min(t1.timestamp_utc) AS earliest_date_in_sample, t2.customer_id FROM public.website_views t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.user_id IS NOT NULL AND t1.page::text !~~ '%media%'::text AND t1.page::text !~~ '%protected%'::text AND (t2.customer_id <> ALL (ARRAY[1, 21])) GROUP BY t2.first_name, t2.last_name, t2.email, t2.phone, t3.customer, t2.customer_id, t1.page ORDER BY t1.page, (count(*)) DESC, t3.customer, t2.first_name, t2.last_name")
    op.update_view('vw_structures_by_model', "SELECT row_number() OVER () AS id, t2.model, max(t1.structure_install_date) AS most_recent_install_date, sum( CASE WHEN (t1.model_type_id IS NOT NULL) THEN 1 ELSE 0 END) AS total_units FROM ((structures t1 LEFT JOIN model_types t2 ON ((t1.model_type_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t1.id))) WHERE (((t2.model)::text <> ALL (ARRAY['SHOP'::text, 'TEST'::text])) AND (t4.customer_id IS DISTINCT FROM 21)) GROUP BY t2.model ORDER BY t2.model;", "SELECT row_number() OVER () AS id, t2.model, max(t1.structure_install_date) AS most_recent_install_date, sum( CASE WHEN t1.model_type_id IS NOT NULL THEN 1 ELSE 0 END) AS total_units FROM structures t1 LEFT JOIN public.model_types t2 ON t1.model_type_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id WHERE (t2.model <> ALL (ARRAY['SHOP'::text, 'TEST'::text])) AND t4.customer_id IS DISTINCT FROM 21 GROUP BY t2.model ORDER BY t2.model")
    op.update_view('gateways', "SELECT DISTINCT ON (gw.id, cust.customer) gw.id AS gateway_id, gw.gateway, gw.aws_thing, gw.mac, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, str.id AS structure_id, str.structure, str.structure_slave_id, str_sl.structure AS structure_slave, gw.ready_and_working, pu.apn, pu.alerts_edge, pu.change_detect_sens, tz.id AS time_zone_id, tz.time_zone, pu.wait_time_mins, pu.wait_time_mins_ol, pu.wait_time_mins_suction, pu.wait_time_mins_discharge, pu.wait_time_mins_spm, pu.wait_time_mins_stboxf, pu.wait_time_mins_hyd_temp, pu.hyd_oil_lvl_thresh, pu.hyd_filt_life_thresh, pu.hyd_oil_life_thresh, pu.wait_time_mins_hyd_oil_lvl, pu.wait_time_mins_hyd_filt_life, pu.wait_time_mins_hyd_oil_life, pu.wait_time_mins_chk_mtr_ovld, pu.wait_time_mins_pwr_fail, pu.wait_time_mins_soft_start_err, pu.wait_time_mins_grey_wire_err, pu.wait_time_mins_ae011, pu.heartbeat_enabled, pu.online_hb_enabled, pu.suction, pu.discharge, pu.spm, pu.stboxf, pu.hyd_temp, str.gps_lat, str.gps_lon, str.downhole, str.surface, str.location, str.well_license, cust.id AS customer_id, cust.customer, cust_sub.id AS cust_sub_group_id, cust_sub.name AS cust_sub_group, cust_sub.abbrev AS cust_sub_group_abbrev, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, mt.unit_type_id AS model_unit_type_id, ut.unit_type AS model_unit_type, str.model_type_id_slave, mt_sl.model AS model_slave, mt_sl.unit_type_id AS model_unit_type_id_slave, ut_sl.unit_type AS model_unit_type_slave, sim.sim_card, (string_to_array((cust.mqtt_topic)::text, ' '::text))[1] AS mqtt_topic, str.status, CASE WHEN (alerts.power_unit_id IS NULL) THEN false ELSE true END AS alerts FROM ((((((((((((((gw gw LEFT JOIN power_units pu ON ((gw.power_unit_id = pu.id))) LEFT JOIN structures str ON ((pu.id = str.power_unit_id))) LEFT JOIN structures str_sl ON ((str.structure_slave_id = str_sl.id))) LEFT JOIN structure_customer_rel str_cust_rel ON ((str_cust_rel.structure_id = str.id))) LEFT JOIN customers cust ON ((str_cust_rel.customer_id = cust.id))) LEFT JOIN ( SELECT DISTINCT alerts_1.power_unit_id FROM alerts alerts_1) alerts ON ((pu.id = alerts.power_unit_id))) LEFT JOIN time_zones tz ON ((str.time_zone_id = tz.id))) LEFT JOIN sim_cards sim ON ((gw.id = sim.gateway_id))) LEFT JOIN model_types mt ON ((str.model_type_id = mt.id))) LEFT JOIN unit_types ut ON ((str.unit_type_id = ut.id))) LEFT JOIN model_types mt_sl ON ((str_sl.model_type_id_slave = mt_sl.id))) LEFT JOIN unit_types ut_sl ON ((str_sl.unit_type_id = ut_sl.id))) LEFT JOIN structure_cust_sub_group_rel csr ON ((csr.structure_id = str.id))) LEFT JOIN cust_sub_groups cust_sub ON ((cust_sub.id = csr.cust_sub_group_id))) ORDER BY gw.id, cust.customer;", "SELECT DISTINCT ON (gw.id, cust.customer) gw.id AS gateway_id, gw.gateway, gw.aws_thing, gw.mac, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, str.id AS structure_id, str.structure, str.structure_slave_id, str_sl.structure AS structure_slave, gw.ready_and_working, pu.apn, pu.alerts_edge, pu.change_detect_sens, tz.id AS time_zone_id, tz.time_zone, pu.wait_time_mins, pu.wait_time_mins_ol, pu.wait_time_mins_suction, pu.wait_time_mins_discharge, pu.wait_time_mins_spm, pu.wait_time_mins_stboxf, pu.wait_time_mins_hyd_temp, pu.hyd_oil_lvl_thresh, pu.hyd_filt_life_thresh, pu.hyd_oil_life_thresh, pu.wait_time_mins_hyd_oil_lvl, pu.wait_time_mins_hyd_filt_life, pu.wait_time_mins_hyd_oil_life, pu.wait_time_mins_chk_mtr_ovld, pu.wait_time_mins_pwr_fail, pu.wait_time_mins_soft_start_err, pu.wait_time_mins_grey_wire_err, pu.wait_time_mins_ae011, pu.heartbeat_enabled, pu.online_hb_enabled, pu.suction, pu.discharge, pu.spm, pu.stboxf, pu.hyd_temp, str.gps_lat, str.gps_lon, str.downhole, str.surface, str.location, str.well_license, cust.id AS customer_id, cust.customer, cust_sub.id AS cust_sub_group_id, cust_sub.name AS cust_sub_group, cust_sub.abbrev AS cust_sub_group_abbrev, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, mt.unit_type_id AS model_unit_type_id, ut.unit_type AS model_unit_type, str.model_type_id_slave, mt_sl.model AS model_slave, mt_sl.unit_type_id AS model_unit_type_id_slave, ut_sl.unit_type AS model_unit_type_slave, sim.sim_card, (string_to_array(cust.mqtt_topic::text, ' '::text))[1] AS mqtt_topic, str.status, CASE WHEN alerts.power_unit_id IS NULL THEN false ELSE true END AS alerts FROM gw gw LEFT JOIN power_units pu ON gw.power_unit_id = pu.id LEFT JOIN structures str ON pu.id = str.power_unit_id LEFT JOIN structures str_sl ON str.structure_slave_id = str_sl.id LEFT JOIN public.structure_customer_rel str_cust_rel ON str_cust_rel.structure_id = str.id LEFT JOIN public.customers cust ON str_cust_rel.customer_id = cust.id LEFT JOIN ( SELECT DISTINCT alerts_1.power_unit_id FROM alerts alerts_1) alerts ON pu.id = alerts.power_unit_id LEFT JOIN public.time_zones tz ON str.time_zone_id = tz.id LEFT JOIN sim_cards sim ON gw.id = sim.gateway_id LEFT JOIN public.model_types mt ON str.model_type_id = mt.id LEFT JOIN public.unit_types ut ON str.unit_type_id = ut.id LEFT JOIN public.model_types mt_sl ON str_sl.model_type_id_slave = mt_sl.id LEFT JOIN public.unit_types ut_sl ON str_sl.unit_type_id = ut_sl.id LEFT JOIN public.structure_cust_sub_group_rel csr ON csr.structure_id = str.id LEFT JOIN public.cust_sub_groups cust_sub ON cust_sub.id = csr.cust_sub_group_id ORDER BY gw.id, cust.customer")
    op.update_view('vw_users_roles', "SELECT id, id AS user_id, name, email, customer, job_title, roles, string_agg(unit, ', '::text) AS units FROM ( SELECT a2.customer, a2.id, a2.name, a2.email, a2.job_title, a2.roles, CASE WHEN ((t7.power_unit_str IS NULL) AND (t6.surface IS NULL)) THEN ''::text ELSE concat(t7.power_unit_str, ' (', t6.surface, ')') END AS unit FROM (((( SELECT a1.customer, a1.id, a1.name, a1.email, a1.job_title, string_agg((a1.role_name)::text, ', '::text) AS roles FROM ( SELECT t2.id, t3.customer, concat(t2.first_name, ' ', t2.last_name) AS name, t2.email, t2.job_title, t4.name AS role_name FROM (((users t2 LEFT JOIN user_role_rel t1 ON ((t1.user_id = t2.id))) LEFT JOIN customers t3 ON ((t3.id = t2.customer_id))) LEFT JOIN roles t4 ON ((t4.id = t1.role_id))) ORDER BY t3.customer, t2.id, t1.role_id) a1 GROUP BY a1.customer, a1.name, a1.email, a1.job_title, a1.id ORDER BY a1.customer, a1.name) a2 LEFT JOIN user_structure_remote_control_rel t5 ON ((t5.user_id = a2.id))) LEFT JOIN structures t6 ON ((t6.id = t5.structure_id))) LEFT JOIN power_units t7 ON ((t7.id = t6.power_unit_id)))) a3 GROUP BY customer, id, name, email, job_title, roles ORDER BY customer, id, name, email, job_title, roles;", "SELECT id, id AS user_id, name, email, customer, job_title, roles, string_agg(unit, ', '::text) AS units FROM ( SELECT a2.customer, a2.id, a2.name, a2.email, a2.job_title, a2.roles, CASE WHEN t7.power_unit_str IS NULL AND t6.surface IS NULL THEN ''::text ELSE concat(t7.power_unit_str, ' (', t6.surface, ')') END AS unit FROM ( SELECT a1.customer, a1.id, a1.name, a1.email, a1.job_title, string_agg(a1.role_name::text, ', '::text) AS roles FROM ( SELECT t2.id, t3.customer, concat(t2.first_name, ' ', t2.last_name) AS name, t2.email, t2.job_title, t4.name AS role_name FROM public.users t2 LEFT JOIN public.user_role_rel t1 ON t1.user_id = t2.id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id LEFT JOIN public.roles t4 ON t4.id = t1.role_id ORDER BY t3.customer, t2.id, t1.role_id) a1 GROUP BY a1.customer, a1.name, a1.email, a1.job_title, a1.id ORDER BY a1.customer, a1.name) a2 LEFT JOIN public.user_structure_remote_control_rel t5 ON t5.user_id = a2.id LEFT JOIN structures t6 ON t6.id = t5.structure_id LEFT JOIN power_units t7 ON t7.id = t6.power_unit_id) a3 GROUP BY customer, id, name, email, job_title, roles ORDER BY customer, id, name, email, job_title, roles")
    op.update_view('vw_gw_cust_sub_group_rel', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t6.id AS cust_sub_groups FROM (((((gw t1 LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN structures t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t3.id))) LEFT JOIN structure_cust_sub_group_rel csr ON ((csr.structure_id = t3.id))) LEFT JOIN cust_sub_groups t6 ON ((t6.id = csr.cust_sub_group_id))) WHERE (t6.id IS NOT NULL) ORDER BY t1.id;', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t6.id AS cust_sub_groups FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id left join public.structure_cust_sub_group_rel csr ON csr.structure_id = t3.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = csr.cust_sub_group_id WHERE t6.id IS NOT NULL ORDER BY t1.id')
    op.update_view('vw_gw_customer_rel', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers FROM ((((gw t1 LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN structures t3 ON ((t3.power_unit_id = t2.id))) LEFT JOIN structure_customer_rel t4 ON ((t4.structure_id = t3.id))) LEFT JOIN customers t5 ON ((t5.id = t4.customer_id))) WHERE (t5.id IS NOT NULL) ORDER BY t1.id;', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id WHERE t5.id IS NOT NULL ORDER BY t1.id')
    op.update_view('vw_gw_structure_rel', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures FROM ((gw t1 LEFT JOIN power_units t2 ON ((t2.id = t1.power_unit_id))) LEFT JOIN structures t3 ON ((t3.power_unit_id = t2.id))) WHERE (t3.id IS NOT NULL) ORDER BY t1.id;', 'SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id WHERE t3.id IS NOT NULL ORDER BY t1.id')
    op.update_view('vw_sales_by_person_month', "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN (t.user_count = 0) THEN (1)::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM (work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON ((t31_1.work_order_id = t2_1.id))) GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN (t2_1.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2_1.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1_1 JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id))) JOIN currencies fx ON ((fx.id = t2_1.currency_id))) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS name_, sum((t1.warranty_cad / (uc.user_count)::numeric)) AS sales_warranty, sum((t1.cost_before_tax_cad / (uc.user_count)::numeric)) AS sales_total, sum( CASE WHEN (t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_labour, sum( CASE WHEN (t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_parts, sum( CASE WHEN (t5.id = 1) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_new_installs, sum( CASE WHEN (t5.id = 4) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_sales, sum( CASE WHEN (t5.id = 2) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_repairs, sum( CASE WHEN (t5.id = 3) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_parts, sum( CASE WHEN (t5.id = 5) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_prevent_maint, sum( CASE WHEN (t5.id = 7) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_rentals FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) LEFT JOIN work_order_user_sales_rel t31 ON ((t31.work_order_id = t2.id))) LEFT JOIN users t3 ON ((t3.id = t31.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) JOIN user_count_per_work_order uc ON ((uc.work_order_id = t2.id))) WHERE (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text);", "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)")
    op.update_view('vw_sales_by_person_quarter', "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN (t.user_count = 0) THEN (1)::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM (work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON ((t31_1.work_order_id = t2_1.id))) GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN (t2_1.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2_1.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1_1 JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id))) JOIN currencies fx ON ((fx.id = t2_1.currency_id))) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('quarter'::text, t2.date_service) AS service_quarter, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS name_, sum((t1.warranty_cad / (uc.user_count)::numeric)) AS sales_warranty, sum((t1.cost_before_tax_cad / (uc.user_count)::numeric)) AS sales_total, sum( CASE WHEN (t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_labour, sum( CASE WHEN (t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_parts, sum( CASE WHEN (t5.id = 1) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_new_installs, sum( CASE WHEN (t5.id = 4) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_sales, sum( CASE WHEN (t5.id = 2) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_repairs, sum( CASE WHEN (t5.id = 3) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_parts, sum( CASE WHEN (t5.id = 5) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_prevent_maint, sum( CASE WHEN (t5.id = 7) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_rentals FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) LEFT JOIN work_order_user_sales_rel t31 ON ((t31.work_order_id = t2.id))) LEFT JOIN users t3 ON ((t3.id = t31.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) JOIN user_count_per_work_order uc ON ((uc.work_order_id = t2.id))) WHERE (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('quarter'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text);", "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('quarter'::text, t2.date_service) AS service_quarter, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('quarter'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)")
    op.update_view('vw_sales_by_person_year', "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN (t.user_count = 0) THEN (1)::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM (work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON ((t31_1.work_order_id = t2_1.id))) GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN (t2_1.is_warranty IS TRUE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS cost_before_tax_cad, sum( CASE WHEN (t2_1.is_warranty IS FALSE) THEN (0)::numeric ELSE (t1_1.cost_before_tax * fx.fx_rate_cad_per) END) AS warranty_cad FROM ((work_orders_parts t1_1 JOIN work_orders t2_1 ON ((t2_1.id = t1_1.work_order_id))) JOIN currencies fx ON ((fx.id = t2_1.currency_id))) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) AS name_, sum((t1.warranty_cad / (uc.user_count)::numeric)) AS sales_warranty, sum((t1.cost_before_tax_cad / (uc.user_count)::numeric)) AS sales_total, sum( CASE WHEN (t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_labour, sum( CASE WHEN (t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text])) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS sales_parts, sum( CASE WHEN (t5.id = 1) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_new_installs, sum( CASE WHEN (t5.id = 4) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_sales, sum( CASE WHEN (t5.id = 2) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_repairs, sum( CASE WHEN (t5.id = 3) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_parts, sum( CASE WHEN (t5.id = 5) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_prevent_maint, sum( CASE WHEN (t5.id = 7) THEN (t1.cost_before_tax_cad / (uc.user_count)::numeric) ELSE (0)::numeric END) AS type_rentals FROM ((((((work_orders_parts_cad t1 JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) LEFT JOIN work_order_user_sales_rel t31 ON ((t31.work_order_id = t2.id))) LEFT JOIN users t3 ON ((t3.id = t31.user_id))) JOIN parts t4 ON ((t4.id = t1.part_id))) JOIN service_types t5 ON ((t5.id = t2.service_type_id))) JOIN user_count_per_work_order uc ON ((uc.work_order_id = t2.id))) WHERE (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY (date_part('year'::text, t2.date_service)), (((t3.first_name)::text || ' '::text) || (t3.last_name)::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, (((t3.first_name)::text || ' '::text) || (t3.last_name)::text);", "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)")
    op.update_view('vw_work_orders_by_unit', 'SELECT DISTINCT ON (wo.id, pu.id, str.id) row_number() OVER () AS id, wo.id AS work_order_id, wo.date_service, cust.customer, wo.service_required, wo.work_done, wo.is_warranty, wo.is_warranty_reason, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, put.name AS power_unit_type, str.id AS structure_id, str.structure, str.downhole, str.surface, mt.model, ut.unit_type FROM ((((((((((work_orders wo LEFT JOIN work_order_power_unit_rel t2 ON ((t2.work_order_id = wo.id))) LEFT JOIN power_units pu ON ((pu.id = t2.power_unit_id))) LEFT JOIN work_order_structure_rel t4 ON ((t4.work_order_id = wo.id))) LEFT JOIN structures str ON ((str.id = t4.structure_id))) LEFT JOIN model_types mt ON ((mt.id = str.model_type_id))) LEFT JOIN unit_types ut ON ((ut.id = mt.unit_type_id))) LEFT JOIN power_unit_types put ON ((put.id = pu.power_unit_type_id))) LEFT JOIN work_order_user_rel wour ON ((wour.work_order_id = wo.id))) LEFT JOIN users users ON ((users.id = wour.user_id))) LEFT JOIN customers cust ON ((wo.customer_id = cust.id)));', 'SELECT DISTINCT ON (wo.id, pu.id, str.id) row_number() OVER () AS id, wo.id AS work_order_id, wo.date_service, cust.customer, wo.service_required, wo.work_done, wo.is_warranty, wo.is_warranty_reason, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, put.name AS power_unit_type, str.id AS structure_id, str.structure, str.downhole, str.surface, mt.model, ut.unit_type FROM work_orders wo LEFT JOIN work_order_power_unit_rel t2 ON t2.work_order_id = wo.id LEFT JOIN power_units pu ON pu.id = t2.power_unit_id LEFT JOIN work_order_structure_rel t4 ON t4.work_order_id = wo.id LEFT JOIN structures str ON str.id = t4.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN public.power_unit_types put ON put.id = pu.power_unit_type_id LEFT JOIN work_order_user_rel wour ON wour.work_order_id = wo.id LEFT JOIN public.users users ON users.id = wour.user_id LEFT JOIN public.customers cust ON wo.customer_id = cust.id')
    op.update_view('vw_work_order_parts_joined', "SELECT t1.id AS work_order_part_id, date_part('year'::text, t2.date_service) AS year, date_part('month'::text, t2.date_service) AS month, t2.date_service, t1.work_order_id, str.structure_str AS structure, str.location, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, pu.power_unit_str AS power_unit, gw.aws_thing AS gateway, t2.timestamp_utc_inserted, t1.part_id, t3.part_num, t3.worksheet AS bom_worksheet, t3.ws_row AS worksheet_row, t3.is_usd AS is_part_usd, t3.cad_per_usd, t3.is_soft_part, t3.msrp_cad, t3.msrp_usd, t3.dealer_cost_cad, t3.dealer_cost_usd, t3.ijack_corp_cost, t3.msrp_mult_cad, t3.msrp_mult_usd, t3.transfer_mult_cad_dealer, t3.transfer_mult_usd_dealer, t3.transfer_mult_inc_to_corp, CASE WHEN (t3.cost_cad IS NULL) THEN ((t1.price)::double precision / t3.msrp_mult_cad) ELSE (t3.cost_cad)::double precision END AS cost_cad, CASE WHEN (t3.cost_usd IS NULL) THEN ((t1.price)::double precision / t3.msrp_mult_usd) ELSE (t3.cost_usd)::double precision END AS cost_usd, t1.description, t1.price, t1.quantity, t1.cost_before_tax, t1.sales_tax_rate, t1.sales_tax_part_amount AS sales_tax, t1.field_tech_id, (((ft.first_name)::text || ' '::text) || (ft.last_name)::text) AS credited_to, t2.requested_by_id, t2.approval_person_id, t2.service_crew, t2.invoice_approval_req, t2.location AS wo_location, t2.has_rcom, t2.service_required, t2.service_resolution, t2.is_warranty, t2.is_warranty_reason, t2.picker_truck, t2.crew_truck, t2.man_lift, t2.trailer, t2.work_done, t2.customer_po, t2.cust_work_order, t2.afe, t2.invoice_summary, t4.customer, (((t5.first_name)::text || ' '::text) || (t5.last_name)::text) AS creator, t10.name AS service_type, t11.name AS inventory_source, t2.creator_company_id, t6.customer AS creator_company, t2.country_id, countries.country_name AS country, t2.currency_id, t12.name AS currency, t13.name AS province, t16.name AS county, cities.name AS city, zip_codes.zip_code, t2.subtotal AS work_order_subtotal, t2.sales_tax AS sales_tax_total, t2.total AS work_order_total, t2.discount_pct, t2.subtotal_after_discount, t2.structure_slave, t14.name AS status, t2.is_paid, t2.notes, t2.safe_work_permit_num, t2.quickbooks_num, (((t15.first_name)::text || ' '::text) || (t15.last_name)::text) AS approved_by, t2.date_due, t2.terms, t2.date_sent_for_approval, t3.part_num_group, CASE WHEN (lower(str.status) ~~ '%%void%%'::text) THEN true ELSE false END AS is_void, CASE WHEN (t2.currency_id = 1) THEN true ELSE false END AS is_usd_work_order FROM (((((((((((((((((((((work_orders_parts t1 LEFT JOIN structures str ON ((str.id = t1.structure_id))) LEFT JOIN model_types mt ON ((mt.id = str.model_type_id))) LEFT JOIN unit_types ut ON ((ut.id = mt.unit_type_id))) LEFT JOIN power_units pu ON ((pu.id = str.power_unit_id))) LEFT JOIN gw gw ON ((gw.power_unit_id = pu.id))) LEFT JOIN work_orders t2 ON ((t2.id = t1.work_order_id))) LEFT JOIN parts t3 ON ((t3.id = t1.part_id))) LEFT JOIN customers t4 ON ((t4.id = t2.customer_id))) LEFT JOIN customers t6 ON ((t6.id = t2.creator_company_id))) LEFT JOIN users t5 ON ((t5.id = t2.creator_id))) LEFT JOIN users ft ON ((ft.id = t1.field_tech_id))) LEFT JOIN service_types t10 ON ((t10.id = t2.service_type_id))) LEFT JOIN inventory_sources t11 ON ((t11.id = t2.inventory_source_id))) LEFT JOIN currencies t12 ON ((t12.id = t2.currency_id))) LEFT JOIN countries countries ON ((countries.id = t2.country_id))) LEFT JOIN provinces t13 ON ((t13.id = t2.province_id))) LEFT JOIN cities cities ON ((cities.id = t2.city_id))) LEFT JOIN zip_codes ON ((zip_codes.id = t2.zip_code_id))) LEFT JOIN work_order_status t14 ON ((t2.status_id = t14.id))) LEFT JOIN users t15 ON ((t15.id = t2.approved_by_id))) LEFT JOIN counties t16 ON ((t16.id = t2.county_id))) WHERE (t2.is_quote = false) ORDER BY t2.id DESC, t1.id;", "SELECT t1.id AS work_order_part_id, date_part('year'::text, t2.date_service) AS year, date_part('month'::text, t2.date_service) AS month, t2.date_service, t1.work_order_id, str.structure_str AS structure, str.location, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, pu.power_unit_str AS power_unit, gw.aws_thing AS gateway, t2.timestamp_utc_inserted, t1.part_id, t3.part_num, t3.worksheet AS bom_worksheet, t3.ws_row AS worksheet_row, t3.is_usd AS is_part_usd, t3.cad_per_usd, t3.is_soft_part, t3.msrp_cad, t3.msrp_usd, t3.dealer_cost_cad, t3.dealer_cost_usd, t3.ijack_corp_cost, t3.msrp_mult_cad, t3.msrp_mult_usd, t3.transfer_mult_cad_dealer, t3.transfer_mult_usd_dealer, t3.transfer_mult_inc_to_corp, CASE WHEN t3.cost_cad IS NULL THEN t1.price::double precision / t3.msrp_mult_cad ELSE t3.cost_cad::double precision END AS cost_cad, CASE WHEN t3.cost_usd IS NULL THEN t1.price::double precision / t3.msrp_mult_usd ELSE t3.cost_usd::double precision END AS cost_usd, t1.description, t1.price, t1.quantity, t1.cost_before_tax, t1.sales_tax_rate, t1.sales_tax_part_amount AS sales_tax, t1.field_tech_id, (ft.first_name::text || ' '::text) || ft.last_name::text AS credited_to, t2.requested_by_id, t2.approval_person_id, t2.service_crew, t2.invoice_approval_req, t2.location AS wo_location, t2.has_rcom, t2.service_required, t2.service_resolution, t2.is_warranty, t2.is_warranty_reason, t2.picker_truck, t2.crew_truck, t2.man_lift, t2.trailer, t2.work_done, t2.customer_po, t2.cust_work_order, t2.afe, t2.invoice_summary, t4.customer, (t5.first_name::text || ' '::text) || t5.last_name::text AS creator, t10.name AS service_type, t11.name AS inventory_source, t2.creator_company_id, t6.customer AS creator_company, t2.country_id, countries.country_name AS country, t2.currency_id, t12.name AS currency, t13.name AS province, t16.name AS county, cities.name AS city, zip_codes.zip_code, t2.subtotal AS work_order_subtotal, t2.sales_tax AS sales_tax_total, t2.total AS work_order_total, t2.discount_pct, t2.subtotal_after_discount, t2.structure_slave, t14.name AS status, t2.is_paid, t2.notes, t2.safe_work_permit_num, t2.quickbooks_num, (t15.first_name::text || ' '::text) || t15.last_name::text AS approved_by, t2.date_due, t2.terms, t2.date_sent_for_approval, t3.part_num_group, CASE WHEN lower(str.status) ~~ '%void%'::text THEN true ELSE false END AS is_void, CASE WHEN t2.currency_id = 1 THEN true ELSE false END AS is_usd_work_order FROM work_orders_parts t1 LEFT JOIN structures str ON str.id = t1.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN power_units pu ON pu.id = str.power_unit_id LEFT JOIN gw gw ON gw.power_unit_id = pu.id LEFT JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN parts t3 ON t3.id = t1.part_id LEFT JOIN public.customers t4 ON t4.id = t2.customer_id LEFT JOIN public.customers t6 ON t6.id = t2.creator_company_id LEFT JOIN public.users t5 ON t5.id = t2.creator_id LEFT JOIN public.users ft ON ft.id = t1.field_tech_id LEFT JOIN service_types t10 ON t10.id = t2.service_type_id LEFT JOIN inventory_sources t11 ON t11.id = t2.inventory_source_id LEFT JOIN currencies t12 ON t12.id = t2.currency_id LEFT JOIN public.countries countries ON countries.id = t2.country_id LEFT JOIN provinces t13 ON t13.id = t2.province_id LEFT JOIN cities cities ON cities.id = t2.city_id LEFT JOIN zip_codes ON zip_codes.id = t2.zip_code_id LEFT JOIN work_order_status t14 ON t2.status_id = t14.id LEFT JOIN public.users t15 ON t15.id = t2.approved_by_id LEFT JOIN counties t16 ON t16.id = t2.county_id WHERE t2.is_quote = false ORDER BY t2.id DESC, t1.id")
    with op.batch_alter_table('zip_codes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('zip_codes_city_id_fkey', 'cities', ['city_id'], ['id'])

    with op.batch_alter_table('zip_code_sales_tax', schema=None) as batch_op:
        batch_op.drop_constraint('zip_code_sales_tax_zip_code_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('zip_code_sales_tax_state_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('zip_code_sales_tax_city_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('zip_code_sales_tax_county_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key('zip_code_sales_tax_state_id_fkey', 'provinces', ['state_id'], ['id'])
        batch_op.create_foreign_key('zip_code_sales_tax_county_id_fkey', 'counties', ['county_id'], ['id'])
        batch_op.create_foreign_key('zip_code_sales_tax_city_id_fkey', 'cities', ['city_id'], ['id'])
        batch_op.create_foreign_key('zip_code_sales_tax_zip_code_id_fkey', 'zip_codes', ['zip_code_id'], ['id'])

    with op.batch_alter_table('work_orders_parts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('gst_rate', sa.NUMERIC(precision=15, scale=2), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('gst_part_amount', sa.NUMERIC(precision=15, scale=2), sa.Computed('\nCASE\n    WHEN (gst_rate IS NULL) THEN NULL::numeric\n    ELSE (((gst_rate / (100)::numeric) * quantity) * price)\nEND', persisted=True), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('pst_rate', sa.NUMERIC(precision=15, scale=2), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('pst_part_amount', sa.NUMERIC(precision=15, scale=2), sa.Computed('\nCASE\n    WHEN (pst_rate IS NULL) THEN NULL::numeric\n    ELSE (((pst_rate / (100)::numeric) * quantity) * price)\nEND', persisted=True), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_orders_parts_warehouse_to_id_fkey', 'warehouses', ['warehouse_to_id'], ['id'])
        batch_op.create_foreign_key('work_orders_parts_fk_work_order_id', 'work_orders', ['work_order_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('work_orders_parts_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('work_orders_parts_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'])
        batch_op.create_foreign_key('work_orders_parts_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('work_orders_parts_structure_id_fkey', 'structures', ['structure_id'], ['id'])
        batch_op.alter_column('sales_tax_rate',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=True)
        batch_op.alter_column('price',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=True)
        batch_op.alter_column('warehouse_id',
               existing_type=sa.INTEGER(),
               comment='Warehouse where parts are sourced from',
               existing_nullable=True)
        batch_op.alter_column('description',
               existing_type=sa.TEXT(),
               nullable=True)
        batch_op.alter_column('part_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('timestamp_utc_inserted',
               existing_type=sa.DATE(),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))

    with op.batch_alter_table('work_orders', schema=None) as batch_op:
        batch_op.add_column(sa.Column('travel_time_hours', sa.NUMERIC(), server_default=sa.text('0'), autoincrement=False, nullable=False, comment='DEPRECATED'))
        batch_op.add_column(sa.Column('company_contact', sa.VARCHAR(), autoincrement=False, nullable=True, comment='DEPRECATED'))
        batch_op.add_column(sa.Column('service_hours', sa.NUMERIC(), autoincrement=False, nullable=True, comment='DEPRECATED'))
        batch_op.add_column(sa.Column('company_contact_email', sa.VARCHAR(), autoincrement=False, nullable=True, comment='DEPRECATED'))
        batch_op.add_column(sa.Column('company_rep', sa.VARCHAR(), autoincrement=False, nullable=True, comment='DEPRECATED'))
        batch_op.add_column(sa.Column('time_start', postgresql.TIME(), autoincrement=False, nullable=True, comment='DEPRECATED'))
        batch_op.add_column(sa.Column('requested_by', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('time_end', postgresql.TIME(), autoincrement=False, nullable=True, comment='DEPRECATED'))
        batch_op.add_column(sa.Column('total_hours', sa.NUMERIC(), sa.Computed('(service_hours + travel_time_hours)', persisted=True), autoincrement=False, nullable=True, comment='DEPRECATED'))
        batch_op.add_column(sa.Column('charged_to', sa.VARCHAR(), autoincrement=False, nullable=True, comment='DEPRECATED'))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_orders_zip_code_id_fkey', 'zip_codes', ['zip_code_id'], ['id'])
        batch_op.create_foreign_key('work_orders_status_id_fkey', 'work_order_status', ['status_id'], ['id'])
        batch_op.create_foreign_key('work_orders_fk_creator_id', 'users', ['creator_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('work_orders_service_type_id_fkey', 'service_types', ['service_type_id'], ['id'])
        batch_op.create_foreign_key('work_orders_city_id_fkey', 'cities', ['city_id'], ['id'])
        batch_op.create_foreign_key('work_orders_fk_customer_id', 'customers', ['customer_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('work_orders_province_id_fkey', 'provinces', ['province_id'], ['id'])
        batch_op.create_foreign_key('work_orders_county_id_fkey', 'counties', ['county_id'], ['id'])
        batch_op.create_foreign_key('work_orders_currency_id_fkey', 'currencies', ['currency_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('work_orders_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'])
        batch_op.create_foreign_key('work_orders_inventory_source_id_fkey', 'inventory_sources', ['inventory_source_id'], ['id'])
        batch_op.alter_column('total',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=True)
        batch_op.alter_column('sales_tax',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=True)
        batch_op.alter_column('discount_pct',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=True)
        batch_op.alter_column('subtotal',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=True)
        batch_op.alter_column('sales_tax_rate',
               existing_type=sa.NUMERIC(precision=15, scale=2),
               nullable=True)
        batch_op.alter_column('service_resolution',
               existing_type=sa.TEXT(),
               comment='DEPRECATED',
               existing_nullable=True)
        batch_op.alter_column('has_rcom',
               existing_type=sa.BOOLEAN(),
               comment='DEPRECATED',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('invoice_approval_req',
               existing_type=sa.BOOLEAN(),
               comment='has invoice been approved already?',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('date_sent_for_approval',
               existing_type=sa.DATE(),
               comment='DEPRECATED',
               existing_nullable=True)
        batch_op.alter_column('approved_by_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('customer_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('is_paid',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               server_default=sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1),
               existing_nullable=False,
               autoincrement=True)

    with op.batch_alter_table('work_order_user_sales_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_order_sales_user_rel_user_id_fkey', 'users', ['user_id'], ['id'], referent_schema='myijack', onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('work_order_user_sales_rel_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('work_order_sales_user_rel_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')

    with op.batch_alter_table('work_order_user_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_order_user_rel_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('work_order_upload_files', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_order_upload_files_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'])

    with op.batch_alter_table('work_order_unit_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_order_unit_type_rel_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('work_order_structure_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_order_structure_rel_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('work_order_structure_rel_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('work_order_service_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_order_service_rel_service_id_fkey', 'service', ['service_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('work_order_service_rel_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('work_order_power_unit_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_order_power_unit_rel_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('work_order_power_unit_rel_power_unit_id_fkey', 'power_units', ['power_unit_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('work_order_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('work_order_model_type_rel_work_order_id_fkey', 'work_orders', ['work_order_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('website_views', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('website_views_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('warehouses_parts_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('warehouses_parts_rel_default_location_id_fkey', 'warehouse_locations', ['default_location_id'], ['id'])
        batch_op.create_foreign_key('warehouses_parts_rel_part_id_fkey', 'parts', ['part_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('warehouses_parts_rel_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'], ondelete='CASCADE')
        batch_op.drop_constraint('uq_warehouse_part', type_='unique')
        batch_op.create_unique_constraint('warehouses_parts_rel_warehouse_id_part_id_key', ['warehouse_id', 'part_id'])
        batch_op.create_index('idx_warehouse_part_reorder', ['warehouse_id', 'warehouse_reorder_point'], unique=False)
        batch_op.create_index('idx_warehouse_part_location', ['default_location_id'], unique=False)
        batch_op.alter_column('quantity_desired',
               existing_type=sa.NUMERIC(),
               nullable=True,
               existing_comment='Desired quantity for re-ordering',
               existing_server_default=sa.text('0'))

    with op.batch_alter_table('warehouses', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('warehouses_province_id_fkey', 'provinces', ['province_id'], ['id'])
        batch_op.create_index('idx_warehouses_province', ['province_id'], unique=False)
        batch_op.create_index('idx_warehouses_name', ['name'], unique=False)
        batch_op.create_index('idx_warehouses_country', ['country_id'], unique=False)
        batch_op.create_index('idx_warehouses_active', ['is_active'], unique=False)
        batch_op.alter_column('time_zone_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               existing_server_default=sa.text('1'))

    with op.batch_alter_table('warehouse_locations', schema=None) as batch_op:
        batch_op.create_table_comment(
        'Storage locations within warehouses (bins, shelves, zones)',
        existing_comment=None
    )
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('warehouse_locations_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'])
        batch_op.create_foreign_key('warehouse_locations_parent_location_id_fkey', 'warehouse_locations', ['parent_location_id'], ['id'])
        batch_op.drop_index(batch_op.f('ix_public_warehouse_locations_location_code'))
        batch_op.create_index('idx_warehouse_locations_warehouse', ['warehouse_id'], unique=False)
        batch_op.create_index('idx_warehouse_locations_parent', ['parent_location_id'], unique=False)
        batch_op.create_index('idx_warehouse_locations_code', ['location_code'], unique=False)

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint('users_fk_customer_id', type_='foreignkey')
        batch_op.create_foreign_key('users_country_id_fkey', 'countries', ['country_id'], ['id'])
        batch_op.create_foreign_key('users_time_zone_id_fkey', 'time_zones', ['time_zone_id'], ['id'])

    with op.batch_alter_table('user_webauthn_credentials', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_webauthn_credentials_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_verification_codes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_verification_codes_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_structure_sales_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_structure_sales_rel_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('user_structure_sales_rel_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_structure_remote_control_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_structure_remote_control_rel_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('user_structure_remote_control_rel_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_structure_operators_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_structure_operators_rel_user_id_fkey', 'users', ['user_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('user_structure_operators_rel_structure_id_fkey', 'structures', ['structure_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')

    with op.batch_alter_table('user_structure_maintenance_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_structure_maintenance_rel_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('user_structure_maintenance_rel_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_role_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_role_rel_role_id_fkey', 'roles', ['role_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('user_role_rel_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_registration_challenges', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_registration_challenges_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_customer_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_customer_rel_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('user_customer_rel_customer_id_fkey', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_cust_sub_group_notify_service_requests_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_cust_sub_group_notify_service_requests_rel_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('user_cust_sub_group_notify_service_reque_cust_sub_group_id_fkey', 'cust_sub_groups', ['cust_sub_group_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_chart_toggles', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_chart_toggles_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_chart_preferences', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_chart_preferences_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_authentication_challenges', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_authentication_challenges_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_api_tokens', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_api_tokens_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('time_zones', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('time_zones_country_id_fkey', 'countries', ['country_id'], ['id'])

    with op.batch_alter_table('surface_patterns', schema=None) as batch_op:
        batch_op.create_table_comment(
        'Contains the surface card training pattern ID and description, for machine learning classification',
        existing_comment=None
    )

    with op.batch_alter_table('surface_images', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('surface_images_pattern_id_fkey', 'surface_patterns', ['pattern_id'], ['id'])
        batch_op.create_index('surface_images_index_cluster_id_ml_version', ['cluster_id', 'ml_version'], unique=False)

    with op.batch_alter_table('structures', schema=None) as batch_op:
        batch_op.add_column(sa.Column('max_spm', sa.REAL(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('max_delta_p', sa.REAL(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('prev_maint_reset_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('unogas_egas', sa.TEXT(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('max_suction', sa.REAL(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('max_discharge_temp', sa.REAL(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('max_discharge', sa.REAL(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('prev_maint_reset_hours', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('structures_province_id_fkey', 'provinces', ['province_id'], ['id'])
        batch_op.create_foreign_key('structures_fk_unit_type_id', 'unit_types', ['unit_type_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('structures_fk_structure_slave_id', 'structures', ['structure_slave_id'], ['id'])
        batch_op.create_foreign_key('structures_shuttle_valve_id_fkey', 'shuttle_valves', ['shuttle_valve_id'], ['id'])
        batch_op.create_foreign_key('structures_packing_gland_id_fkey', 'packing_glands', ['packing_gland_id'], ['id'])
        batch_op.create_foreign_key('structures_fk_time_zone_id', 'time_zones', ['time_zone_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('structures_check_valve_id_fkey', 'check_valves', ['check_valve_id'], ['id'])
        batch_op.create_foreign_key('structures_hyd_piston_type_id_fkey', 'hyd_piston_types', ['hyd_piston_type_id'], ['id'])
        batch_op.create_foreign_key('structures_barrel_id_fkey', 'barrels', ['barrel_id'], ['id'])
        batch_op.create_foreign_key('fk_structures_hyd_piston_type_id', 'hyd_piston_types', ['hyd_piston_type_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('structures_fk_model_type_id', 'model_types', ['model_type_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('structures_rod_id_fkey', 'rods', ['rod_id'], ['id'])
        batch_op.create_foreign_key('structures_fk_check_valve_id', 'check_valves', ['check_valve_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('structures_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'])
        batch_op.create_foreign_key('structures_fk_power_unit_id', 'power_units', ['power_unit_id'], ['id'])
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('warehouse_id',
               existing_type=sa.INTEGER(),
               comment='Default warehouse assigned to this structure for parts inventory',
               existing_nullable=True)
        batch_op.alter_column('has_rcom',
               existing_type=sa.BOOLEAN(),
               comment="Certain units like DGAS, and certain customers like XTO, won't have SIM cards\n",
               existing_nullable=False,
               existing_server_default=sa.text('true'))

    with op.batch_alter_table('structure_customer_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('structure_customer_rel_customer_id_fkey', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('structure_customer_rel_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('structure_cust_sub_group_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('structure_cust_sub_group_rel_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('structure_cust_sub_group_rel_cust_sub_group_id_fkey', 'cust_sub_groups', ['cust_sub_group_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('sim_cards', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('sim_cards_fk_customer_id', 'customers', ['customer_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('sim_cards_fk_gateway_id', 'gw', ['gateway_id'], ['id'])
        batch_op.create_index('sim_cards_index', ['sim_card', 'gateway_id', 'customer_id'], unique=False)
        batch_op.alter_column('sim_card',
               existing_type=sa.TEXT(),
               nullable=False)

    with op.batch_alter_table('service_emailees', schema=None) as batch_op:
        batch_op.create_table_comment(
        "who to email when there's a service request",
        existing_comment=None
    )
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('service_clock', schema=None) as batch_op:
        batch_op.add_column(sa.Column('time_zone_id', sa.INTEGER(), server_default=sa.text('2'), autoincrement=False, nullable=False))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('service_clock_time_zone_id_out_fkey', 'time_zones', ['time_zone_out_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('service_clock_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'])
        batch_op.create_foreign_key('service_clock_time_zone_id_fkey', 'time_zones', ['time_zone_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('service_clock_structure_id_fkey', 'structures', ['structure_id'], ['id'])
        batch_op.create_foreign_key('service_clock_time_zone_id_in_fkey', 'time_zones', ['time_zone_in_id'], ['id'], referent_schema='myijack')
        batch_op.alter_column('time_zone_out_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('time_zone_in_id',
               existing_type=sa.INTEGER(),
               nullable=True)

    with op.batch_alter_table('service', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('service_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('service_service_type_id_fkey', 'service_types', ['service_type_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('sales_taxes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('sales_taxes_province_id_fkey', 'provinces', ['province_id'], ['id'])

    with op.batch_alter_table('report_email_op_hours_warehouses_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_op_hours_warehouses_rel_report_id_fkey', 'report_email_op_hours', ['report_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_op_hours_warehouses_rel_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours_unit_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_op_hours_unit_types__report_email_op_hours_id_fkey', 'report_email_op_hours', ['report_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_op_hours_unit_types_rel_unit_type_id_fkey', 'unit_types', ['unit_type_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_op_hours_unit_types_rel_report_id_fkey', 'report_email_op_hours', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_op_hours_types_rel_report_id_fkey', 'report_email_op_hours', ['report_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_op_hours_types_r_report_email_op_hours_type_i_fkey', 'report_email_op_hours_types', ['report_email_op_hours_type_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_op_hours_types_rel_report_email_op_hours_id_fkey', 'report_email_op_hours', ['report_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours_model_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_op_hours_model_types_rel_report_id_fkey', 'report_email_op_hours', ['report_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_op_hours_model_types_report_email_op_hours_id_fkey', 'report_email_op_hours', ['report_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_op_hours_model_types_rel_model_type_id_fkey', 'model_types', ['model_type_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_op_hours', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('report_email_inventory_warehouses_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_inventory_warehouses_rel_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_inventory_warehouses_rel_report_id_fkey', 'report_email_inventory', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_inventory_hours_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_inventory_hours_rel_hour_fkey', 'hours', ['hour'], ['hour'])
        batch_op.create_foreign_key('report_email_inventory_hours_rel_report_id_fkey', 'report_email_inventory', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_inventory_days_of_week_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_inventory_days_of_week_rel_day_of_week_id_fkey', 'days_of_week', ['day_of_week_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_inventory_days_of_week_rel_report_id_fkey', 'report_email_inventory', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_inventory', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('report_email_hourly_warehouses_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_hourly_warehouses_rel_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_hourly_warehouses_rel_report_id_fkey', 'report_email_hourly', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_unit_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_units_down_unit_types_rel_fk_report_units_down', 'report_email_hourly', ['report_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_hourly_unit_types_rel_report_id_fkey', 'report_email_hourly', ['report_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_units_down_unit_types_rel_fk_unit_types', 'unit_types', ['unit_type_id'], ['id'], referent_schema='myijack', onupdate='CASCADE', ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_model_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_units_down_model_types_rel_fk_report_units_down', 'report_email_hourly', ['report_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('report_units_down_model_types_rel_fk_model_types', 'model_types', ['model_type_id'], ['id'], referent_schema='myijack', onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_hourly_model_types_rel_report_id_fkey', 'report_email_hourly', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_hours_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_units_down_hours_rel_fk_report_units_down', 'report_email_hourly', ['report_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_hourly_hours_rel_hour_fkey', 'hours', ['hour'], ['hour'])
        batch_op.create_foreign_key('report_email_hourly_hours_rel_report_id_fkey', 'report_email_hourly', ['report_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_units_down_hours_rel_fk_hours', 'hours', ['hour'], ['hour'], onupdate='CASCADE', ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly_days_of_week_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_hourly_days_of_week_rel_day_of_week_id_fkey', 'days_of_week', ['day_of_week_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_hourly_days_of_week_rel_report_id_fkey', 'report_email_hourly', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_hourly', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_units_down_fk_user_id', 'users', ['user_id'], ['id'], referent_schema='myijack')

    with op.batch_alter_table('report_email_derates_warehouses_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_derates_warehouses_rel_report_id_fkey', 'report_email_derates', ['report_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_derates_warehouses_rel_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_unit_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_derates_unit_types_rel_report_id_fkey', 'report_email_derates', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_model_types_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_derates_model_types_rel_report_id_fkey', 'report_email_derates', ['report_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_derates_model_types_rel_model_type_id_fkey', 'model_types', ['model_type_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_hours_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_derates_hours_rel_hour_fkey', 'hours', ['hour'], ['hour'])
        batch_op.create_foreign_key('report_email_derates_hours_rel_report_id_fkey', 'report_email_derates', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates_days_of_week_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('report_email_derates_days_of_week_rel_day_of_week_id_fkey', 'days_of_week', ['day_of_week_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('report_email_derates_days_of_week_rel_report_id_fkey', 'report_email_derates', ['report_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('report_email_derates', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('remote_control', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('remote_control_power_unit_id_fkey', 'power_units', ['power_unit_id'], ['id'])
        batch_op.create_foreign_key('remote_control_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.alter_column('dev_test_prd',
               existing_type=sa.TEXT(),
               type_=sa.VARCHAR(),
               existing_nullable=False)

    with op.batch_alter_table('release_notes', schema=None) as batch_op:
        batch_op.add_column(sa.Column('checksum', sa.VARCHAR(), autoincrement=False, nullable=True))

    with op.batch_alter_table('provinces', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('profiler_measurements', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('profiler_measurements_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('power_units_modbus_networks', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_units_modbus_networks_power_unit_id_fkey', 'power_units', ['power_unit_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('power_units_fixed_ip_networks', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_units_fixed_ip_networks_power_unit_id_fkey', 'power_units', ['power_unit_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('power_units', schema=None) as batch_op:
        batch_op.add_column(sa.Column('ip_modbus', sa.VARCHAR(length=15), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('subnet_modbus', sa.VARCHAR(length=2), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('gateway_modbus', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_units_fk_power_unit_type_id', 'power_unit_types', ['power_unit_type_id'], ['id'], referent_schema='myijack')
        batch_op.create_index('power_units_index_power_unit', ['power_unit'], unique=False)
        batch_op.alter_column('wait_time_mins_hyd_oil_life',
               existing_type=sa.INTEGER(),
               type_=sa.SMALLINT(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('wait_time_mins_hyd_filt_life',
               existing_type=sa.INTEGER(),
               type_=sa.SMALLINT(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('wait_time_mins_hyd_oil_lvl',
               existing_type=sa.INTEGER(),
               type_=sa.SMALLINT(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('wait_time_mins_hyd_temp',
               existing_type=sa.INTEGER(),
               type_=sa.SMALLINT(),
               existing_nullable=False,
               existing_server_default=sa.text('60'))
        batch_op.alter_column('wait_time_mins_spm',
               existing_type=sa.SMALLINT(),
               nullable=False,
               existing_server_default=sa.text('60'))
        batch_op.alter_column('change_detect_sens',
               existing_type=sa.NUMERIC(),
               nullable=False,
               existing_server_default=sa.text('0.8'))
        batch_op.alter_column('alerts_edge',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('website_card_msg',
               existing_type=sa.BOOLEAN(),
               comment='Put "Artificial Intelligence" messages on the card tab on RCOM?',
               existing_nullable=False,
               existing_server_default=sa.text('true'))

    with op.batch_alter_table('power_unit_types_voltage', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_unit_types_voltage_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('power_unit_types_speeds', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_unit_types_speeds_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('power_unit_types_power', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_unit_types_power_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('power_unit_types_parts_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_unit_types_parts_rel_power_unit_type_id_fkey', 'power_unit_types', ['power_unit_type_id'], ['id'])
        batch_op.create_foreign_key('power_unit_types_parts_rel_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('power_unit_types_options', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_unit_types_options_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('power_unit_types_filters_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_unit_types_filters_rel_power_unit_type_id_fkey', 'power_unit_types', ['power_unit_type_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('power_unit_types_filters_rel_part_filter_id_fkey', 'part_filters', ['part_filter_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('power_unit_types', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('power_unit_types_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('parts', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='Master table for all inventory parts and items'
    )
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('parts_created_by_id_fkey', 'users', ['created_by_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('parts_category_id_fkey', 'part_categories', ['category_id'], ['id'])
        batch_op.create_foreign_key('parts_updated_by_id_fkey', 'users', ['updated_by_id'], ['id'], referent_schema='myijack')
        batch_op.drop_index(batch_op.f('ix_public_parts_part_num'))
        batch_op.create_unique_constraint('parts_unique_part_num', ['part_num'])
        batch_op.create_index('idx_parts_updated_by_id', ['updated_by_id'], unique=False)
        batch_op.create_index('idx_parts_unit_of_measure', ['unit_of_measure'], unique=False)
        batch_op.create_index('idx_parts_is_serialized', ['is_serialized'], unique=False)
        batch_op.create_index('idx_parts_is_sellable', ['is_sellable'], unique=False)
        batch_op.create_index('idx_parts_is_purchasable', ['is_purchasable'], unique=False)
        batch_op.create_index('idx_parts_is_lot_tracked', ['is_lot_tracked'], unique=False)
        batch_op.create_index('idx_parts_is_active', ['is_active'], unique=False)
        batch_op.create_index('idx_parts_created_by_id', ['created_by_id'], unique=False)
        batch_op.create_index('idx_parts_category_id', ['category_id'], unique=False)
        batch_op.alter_column('updated_by_id',
               existing_type=sa.INTEGER(),
               comment='User who last updated this part record',
               existing_nullable=False,
               existing_server_default=sa.text('1'))
        batch_op.alter_column('created_by_id',
               existing_type=sa.INTEGER(),
               comment='User who created this part record',
               existing_nullable=False,
               existing_server_default=sa.text('1'))
        batch_op.alter_column('is_sellable',
               existing_type=sa.BOOLEAN(),
               comment='Whether this part can be sold',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('is_purchasable',
               existing_type=sa.BOOLEAN(),
               comment='Whether this part can be purchased',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               comment='Whether this part is active in the system',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('lead_time_days',
               existing_type=sa.INTEGER(),
               comment='Lead time in days for procurement',
               existing_nullable=True)
        batch_op.alter_column('reorder_quantity',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment='Quantity to order when reorder point is reached',
               existing_nullable=True)
        batch_op.alter_column('reorder_point',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment='Inventory level that triggers reorder',
               existing_nullable=True)
        batch_op.alter_column('max_stock_level',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment='Maximum stock level for inventory control',
               existing_nullable=True)
        batch_op.alter_column('min_stock_level',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment='Minimum stock level before reorder alert',
               existing_nullable=True)
        batch_op.alter_column('is_lot_tracked',
               existing_type=sa.BOOLEAN(),
               comment='Whether this part requires lot/batch tracking',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('is_serialized',
               existing_type=sa.BOOLEAN(),
               comment='Whether this part requires serial number tracking',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('unit_cost',
               existing_type=sa.NUMERIC(precision=12, scale=4),
               comment='Computed column: cost per unit in CAD (references cost_cad)',
               existing_nullable=True)
        batch_op.alter_column('unit_of_measure',
               existing_type=sa.VARCHAR(length=20),
               comment='Unit of measure (EACH, LB, KG, etc.)',
               existing_nullable=False,
               existing_server_default=sa.text("'EACH'::character varying"))
        batch_op.alter_column('category_id',
               existing_type=sa.INTEGER(),
               comment='Foreign key to part_categories table for organizing parts',
               existing_nullable=True)
        batch_op.alter_column('is_usd',
               existing_type=sa.BOOLEAN(),
               nullable=True)
        batch_op.alter_column('no_delete',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               comment='This indicates whether the part should remain in the database even if it\'s no longer used in the BOM Master spreadsheet. Things like "miscellaneous" for the work order part line items, should be "true" for "no_delete"',
               existing_server_default=sa.text('false'))

    with op.batch_alter_table('part_categories', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('part_categories_parent_category_id_fkey', 'part_categories', ['parent_category_id'], ['id'])
        batch_op.drop_index(batch_op.f('ix_public_part_categories_code'))
        batch_op.create_unique_constraint('part_categories_code_key', ['code'])
        batch_op.create_index('idx_part_categories_code', ['code'], unique=False)

    with op.batch_alter_table('mqtt_messages', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('mqtt_messages_gateway_id_fkey', 'gw', ['gateway_id'], ['id'], ondelete='CASCADE')
        batch_op.alter_column('rc_message',
               existing_type=sa.VARCHAR(),
               comment='message from MQTT operation (e.g. "MQTT_ERR_SUCCESS")',
               existing_nullable=False)
        batch_op.alter_column('rc',
               existing_type=sa.SMALLINT(),
               comment='return code from MQTT operation',
               existing_nullable=False,
               existing_server_default=sa.text('0'))

    with op.batch_alter_table('model_types_parts_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('model_types_parts_rel_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('model_types_parts_rel_model_type_id_fkey', 'model_types', ['model_type_id'], ['id'])

    with op.batch_alter_table('model_types_parts_pm_seal_kits_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('model_types_parts_pm_seal_kits_rel_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('model_types_parts_pm_seal_kits_rel_model_type_id_fkey', 'model_types', ['model_type_id'], ['id'])

    with op.batch_alter_table('model_types_options_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('model_types_options_rel_model_id_fkey', 'parts', ['model_id'], ['id'])
        batch_op.create_foreign_key('model_types_options_rel_option_id_fkey', 'parts', ['option_id'], ['id'])

    with op.batch_alter_table('model_types_options', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('model_types_options_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('model_types', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('model_types_unit_type_id_fkey', 'unit_types', ['unit_type_id'], ['id'])
        batch_op.create_foreign_key('model_types_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('modbus_holding_registers', schema=None) as batch_op:
        batch_op.create_table_comment(
        'Mapping the abbrev/metric from the map_abbrev_item table to the holding register and number of registers',
        existing_comment=None
    )
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('modbus_holding_registers_abbrev_id_fkey', 'map_abbrev_item', ['abbrev_id'], ['id'])
        batch_op.alter_column('holding_reg_40k',
               existing_type=sa.SMALLINT(),
               type_=sa.INTEGER(),
               existing_nullable=False)

    with op.batch_alter_table('maintenance', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('maintenance_power_unit_id_fkey', 'power_units', ['power_unit_id'], ['id'])
        batch_op.create_foreign_key('maintenance_maintenance_type_id_fkey', 'maintenance_types', ['maintenance_type_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('maintenance_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')
        batch_op.alter_column('maintenance_type_id',
               existing_type=sa.INTEGER(),
               nullable=True)

    with op.batch_alter_table('inventory_reservations', schema=None) as batch_op:
        batch_op.add_column(sa.Column('work_order_part_id', sa.INTEGER(), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('inventory_reservations_created_by_id_fkey', 'users', ['created_by_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('inventory_reservations_warehouse_to_id_fkey', 'warehouses', ['warehouse_to_id'], ['id'])
        batch_op.create_foreign_key('inventory_reservations_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('inventory_reservations_cancelled_by_id_fkey', 'users', ['cancelled_by_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('inventory_reservations_work_order_part_id_fkey', 'work_orders_parts', ['work_order_part_id'], ['id'])
        batch_op.create_foreign_key('inventory_reservations_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'])
        batch_op.drop_index(batch_op.f('ix_public_inventory_reservations_reservation_number'))
        batch_op.create_unique_constraint('inventory_reservations_reservation_number_key', ['reservation_number'])
        batch_op.create_index('idx_reservation_part_warehouse', ['part_id', 'warehouse_id'], unique=False)
        batch_op.create_index('idx_reservation_number', ['reservation_number'], unique=False)

    with op.batch_alter_table('inventory_movements', schema=None) as batch_op:
        batch_op.add_column(sa.Column('work_order_part_id', sa.INTEGER(), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('inventory_movements_reversal_movement_id_fkey', 'inventory_movements', ['reversal_movement_id'], ['id'])
        batch_op.create_foreign_key('inventory_movements_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('inventory_movements_to_location_id_fkey', 'warehouse_locations', ['to_location_id'], ['id'])
        batch_op.create_foreign_key('inventory_movements_from_warehouse_id_fkey', 'warehouses', ['from_warehouse_id'], ['id'])
        batch_op.create_foreign_key('inventory_movements_from_location_id_fkey', 'warehouse_locations', ['from_location_id'], ['id'])
        batch_op.create_foreign_key('inventory_movements_created_by_id_fkey', 'users', ['created_by_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('inventory_movements_to_warehouse_id_fkey', 'warehouses', ['to_warehouse_id'], ['id'])
        batch_op.create_foreign_key('inventory_movements_work_order_part_id_fkey', 'work_orders_parts', ['work_order_part_id'], ['id'])
        batch_op.drop_index(batch_op.f('ix_public_inventory_movements_movement_number'))
        batch_op.create_unique_constraint('inventory_movements_movement_number_key', ['movement_number'])
        batch_op.create_index('idx_movement_type', ['movement_type'], unique=False)
        batch_op.create_index('idx_movement_reference', ['reference_type', 'reference_id'], unique=False)
        batch_op.create_index('idx_movement_number', ['movement_number'], unique=False)

    with op.batch_alter_table('inventory_ledger', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('inventory_ledger_created_by_id_fkey', 'users', ['created_by_id'], ['id'])
        batch_op.create_foreign_key('inventory_ledger_reversal_of_id_fkey', 'inventory_ledger', ['reversal_of_id'], ['id'])
        batch_op.create_foreign_key('inventory_ledger_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('inventory_ledger_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'])

    with op.batch_alter_table('hours', schema=None) as batch_op:
        batch_op.alter_column('hour_ending',
               existing_type=sa.SMALLINT(),
               nullable=False,
               existing_server_default=sa.text('1'))

    with op.batch_alter_table('gw_tested_cellular', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('gw_tested_cellular_network_id_fkey', 'gw_cell_networks', ['network_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('gw_tested_cellular_gateway_id_fkey', 'gw', ['gateway_id'], ['id'], ondelete='CASCADE')
        batch_op.alter_column('timestamp_utc',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DATE(),
               existing_nullable=False)

    with op.batch_alter_table('gw_not_connected_dont_worry', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('gw_not_connected_dont_worry_gateway_id_fkey', 'gw', ['gateway_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('gw_info', schema=None) as batch_op:
        batch_op.add_column(sa.Column('gw_type_reported', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('gw_info_gateway_id_fkey', 'gw', ['gateway_id'], ['id'])
        batch_op.alter_column('has_slave',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
        batch_op.alter_column('timestamp_utc_last_reported',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)

    with op.batch_alter_table('gw', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('gw_power_unit_id_fkey', 'power_units', ['power_unit_id'], ['id'])
        batch_op.create_foreign_key('gw_structure_id_fkey', 'structures', ['structure_id'], ['id'])
        batch_op.create_foreign_key('gw_gateway_type_id_fkey', 'gateway_types', ['gateway_type_id'], ['id'])
        batch_op.create_index('gw_index_gateway_structure_id_power_unit_id', ['gateway', 'structure_id', 'power_unit_id'], unique=False)
        batch_op.create_unique_constraint('gw_id_unique', ['id'])

    with op.batch_alter_table('geocoding_cache', schema=None) as batch_op:
        batch_op.create_table_comment(
        'Cache table for reverse geocoding results to improve performance and reduce external API calls',
        existing_comment=None
    )
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('geocoding_cache_country_id_fkey', 'countries', ['country_id'], ['id'])
        batch_op.create_foreign_key('geocoding_cache_province_id_fkey', 'provinces', ['province_id'], ['id'])
        batch_op.create_index('idx_geocoding_cache_province_id', ['province_id'], unique=False)
        batch_op.create_index('idx_geocoding_cache_created_at', ['created_at'], unique=False)
        batch_op.create_index('idx_geocoding_cache_country_id', ['country_id'], unique=False)
        batch_op.create_index('idx_geocoding_cache_coordinates', ['lat_rounded', 'lon_rounded'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='Timestamp when the cache entry was last updated',
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='Timestamp when the cache entry was created',
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
        batch_op.alter_column('data_source',
               existing_type=sa.VARCHAR(length=20),
               comment='Source of the geocoding data (nominatim, google, etc.)',
               existing_nullable=True,
               existing_server_default=sa.text("'nominatim'::character varying"))
        batch_op.alter_column('confidence_score',
               existing_type=sa.REAL(),
               comment='Confidence score of the geocoding result (0.0 to 1.0)',
               existing_nullable=True,
               existing_server_default=sa.text('1.0'))
        batch_op.alter_column('locality',
               existing_type=sa.VARCHAR(length=100),
               comment='City or locality name from geocoding result',
               existing_nullable=True)
        batch_op.alter_column('province_id',
               existing_type=sa.INTEGER(),
               comment='Foreign key reference to provinces table',
               existing_nullable=True)
        batch_op.alter_column('country_id',
               existing_type=sa.INTEGER(),
               comment='Foreign key reference to countries table',
               existing_nullable=True)
        batch_op.alter_column('lon_rounded',
               existing_type=sa.NUMERIC(precision=8, scale=5),
               comment='Longitude rounded to 5 decimal places for caching efficiency',
               existing_nullable=False)
        batch_op.alter_column('lat_rounded',
               existing_type=sa.NUMERIC(precision=8, scale=5),
               comment='Latitude rounded to 5 decimal places for caching efficiency',
               existing_nullable=False)

    with op.batch_alter_table('flask_dance_oauth', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('flask_dance_oauth_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('error_logs', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('error_logs_resolved_by_user_id_fkey', 'users', ['resolved_by_user_id'], ['id'], ondelete='SET NULL')
        batch_op.create_foreign_key('error_logs_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='SET NULL')

    with op.batch_alter_table('days_of_week', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('id',
               existing_type=sa.SMALLINT(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('days_of_week_id_seq'::regclass)"))

    with op.batch_alter_table('cycle_counts', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('cycle_counts_assigned_to_id_fkey', 'users', ['assigned_to_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('cycle_counts_created_by_id_fkey', 'users', ['created_by_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('cycle_counts_warehouse_id_fkey', 'warehouses', ['warehouse_id'], ['id'])
        batch_op.drop_index(batch_op.f('ix_public_cycle_counts_count_number'))
        batch_op.create_index('idx_cycle_count_warehouse', ['warehouse_id'], unique=False)
        batch_op.create_index('idx_cycle_count_status', ['status'], unique=False)
        batch_op.create_index('idx_cycle_count_scheduled', ['scheduled_date'], unique=False)
        batch_op.create_index('idx_cycle_count_number', ['count_number'], unique=False)
        batch_op.create_unique_constraint('cycle_counts_count_number_key', ['count_number'])

    with op.batch_alter_table('cycle_count_items', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('cycle_count_items_adjustment_movement_id_fkey', 'inventory_movements', ['adjustment_movement_id'], ['id'])
        batch_op.create_foreign_key('cycle_count_items_location_id_fkey', 'warehouse_locations', ['location_id'], ['id'])
        batch_op.create_foreign_key('cycle_count_items_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('cycle_count_items_counted_by_id_fkey', 'users', ['counted_by_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('cycle_count_items_cycle_count_id_fkey', 'cycle_counts', ['cycle_count_id'], ['id'])
        batch_op.create_index('idx_cycle_count_items_part', ['part_id'], unique=False)
        batch_op.create_index('idx_cycle_count_items_location', ['location_id'], unique=False)
        batch_op.create_index('idx_cycle_count_items_count', ['cycle_count_id'], unique=False)

    with op.batch_alter_table('customers', schema=None) as batch_op:
        batch_op.drop_constraint('customers_accounting_contact_id_fkey', type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('customers_province_id_fkey', 'provinces', ['province_id'], ['id'])
        batch_op.create_foreign_key('customers_country_id_fkey', 'countries', ['country_id'], ['id'])

    with op.batch_alter_table('cust_sub_groups', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('cust_sub_groups_customer_id_fkey', 'customers', ['customer_id'], ['id'])

    with op.batch_alter_table('currencies_rates', schema=None) as batch_op:
        batch_op.create_table_comment(
        'Historical currency exchange rates',
        existing_comment=None
    )
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('fk_currencies_rates_currency_id', 'currencies', ['currency_id'], ['id'], ondelete='CASCADE')
        batch_op.create_index('idx_currencies_rates_rate_date', ['rate_date'], unique=False)
        batch_op.create_index('idx_currencies_rates_currency_id', ['currency_id'], unique=False)
        batch_op.create_index('idx_currencies_rates_currency_date', ['currency_id', 'rate_date'], unique=False)
        batch_op.alter_column('source',
               existing_type=sa.VARCHAR(length=50),
               comment='Data source (e.g., exchangerate-api.com)',
               existing_nullable=False,
               existing_server_default=sa.text("'exchangerate-api.com'::character varying"))
        batch_op.alter_column('fx_rate_cad_per',
               existing_type=sa.NUMERIC(precision=10, scale=6),
               comment='Exchange rate: CAD per unit of currency',
               existing_nullable=False)
        batch_op.alter_column('rate_date',
               existing_type=sa.DATE(),
               comment='Date of the exchange rate',
               existing_nullable=False)
        batch_op.alter_column('currency_id',
               existing_type=sa.INTEGER(),
               comment='Foreign key to currencies table',
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment='Primary key',
               existing_nullable=False,
               autoincrement=True)

    with op.batch_alter_table('currencies', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.alter_column('fx_rate_cad_per',
               existing_type=sa.NUMERIC(),
               comment='CAD cost per unit of this currency',
               existing_nullable=False,
               existing_server_default=sa.text('1'))

    with op.batch_alter_table('counties', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('counties_province_id_fkey', 'provinces', ['province_id'], ['id'])

    with op.batch_alter_table('contact_form', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('contact_form_user_id_fkey', 'users', ['user_id'], ['id'])

    with op.batch_alter_table('compression_images', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('compression_images_fk_pattern_id', 'compression_patterns', ['pattern_id'], ['id'])
        batch_op.create_index('compression_images_index_cluster_id_ml_version', ['cluster_id', 'ml_version'], unique=False)

    with op.batch_alter_table('cities', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('cities_province_id_fkey', 'provinces', ['province_id'], ['id'])
        batch_op.create_foreign_key('cities_county_id_fkey', 'counties', ['county_id'], ['id'])

    with op.batch_alter_table('career_files', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('career_files_fk_user_id', 'users', ['user_id'], ['id'], referent_schema='myijack', ondelete='CASCADE')

    with op.batch_alter_table('career_applications_files_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('career_application_files_career_file_id_fkey', 'career_files', ['career_file_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('career_applications_files_rel_career_file_id_fkey', 'career_files', ['career_file_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('career_applications_files_rel_career_application_id_fkey', 'career_applications', ['career_application_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('career_application_files_career_application_id_fkey', 'career_applications', ['career_application_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')

    with op.batch_alter_table('career_applications', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('career_applications_fk_user_id', 'users', ['user_id'], ['id'], referent_schema='myijack', ondelete='CASCADE')

    with op.batch_alter_table('calories', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('calories_user_id_fkey', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('calories_calorie_type_id_fkey', 'calorie_types', ['calorie_type_id'], ['id'])

    with op.batch_alter_table('calculators', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('calculators_model_type_id_fkey', 'model_types', ['model_type_id'], ['id'])
        batch_op.create_foreign_key('calculators_power_unit_type_id_fkey', 'power_unit_types', ['power_unit_type_id'], ['id'])

    with op.batch_alter_table('bom_structure_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_structure_part_rel_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('bom_structure_part_rel_finished_good_id_fkey', 'bom_structure', ['finished_good_id'], ['id'])

    with op.batch_alter_table('bom_structure_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_structure_model_type_rel_finished_good_id_fkey', 'bom_structure', ['finished_good_id'], ['id'])

    with op.batch_alter_table('bom_pump_top_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_pump_top_part_rel_finished_good_id_fkey', 'bom_pump_top', ['finished_good_id'], ['id'])
        batch_op.create_foreign_key('bom_pump_top_part_rel_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('bom_pump_top_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_pump_top_model_type_rel_finished_good_id_fkey', 'bom_pump_top', ['finished_good_id'], ['id'])

    with op.batch_alter_table('bom_pricing_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_pricing_part_rel_finished_good_id_fkey', 'bom_pricing', ['finished_good_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('bom_pricing_part_rel_part_id_fkey', 'parts', ['part_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('bom_pricing_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_pricing_model_type_rel_model_type_id_fkey', 'model_types', ['model_type_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('bom_pricing_model_type_rel_finished_good_id_fkey', 'bom_pricing', ['finished_good_id'], ['id'])

    with op.batch_alter_table('bom_powerunit_power_unit_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_powerunit_power_unit_type_power_unit_type_id_fkey', 'power_unit_types', ['power_unit_type_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('bom_powerunit_power_unit_type_finished_good_id_fkey', 'bom_powerunit', ['finished_good_id'], ['id'])

    with op.batch_alter_table('bom_powerunit_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_powerunit_part_rel_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('bom_powerunit_part_rel_finished_good_id_fkey', 'bom_powerunit', ['finished_good_id'], ['id'])

    with op.batch_alter_table('bom_dgas_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_dgas_part_rel_finished_good_id_fkey', 'bom_dgas', ['finished_good_id'], ['id'])
        batch_op.create_foreign_key('bom_dgas_part_rel_part_id_fkey', 'parts', ['part_id'], ['id'])

    with op.batch_alter_table('bom_dgas_model_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_dgas_model_type_rel_finished_good_id_fkey', 'bom_dgas', ['finished_good_id'], ['id'])

    with op.batch_alter_table('bom_base_powerunit_power_unit_type_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_base_powerunit_power_unit_type_rel_finished_good_id_fkey', 'bom_base_powerunit', ['finished_good_id'], ['id'])

    with op.batch_alter_table('bom_base_powerunit_part_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('bom_base_powerunit_part_rel_part_id_fkey', 'parts', ['part_id'], ['id'])
        batch_op.create_foreign_key('bom_base_powerunit_part_rel_finished_good_id_fkey', 'bom_base_powerunit', ['finished_good_id'], ['id'])

    with op.batch_alter_table('applications', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('applications_user_id_fkey', 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key('applications_customer_id_fkey', 'customers', ['customer_id'], ['id'])
        batch_op.create_foreign_key('applications_province_id_fkey', 'provinces', ['province_id'], ['id'])
        batch_op.create_foreign_key('applications_application_type_id_fkey', 'application_types', ['application_type_id'], ['id'])

    with op.batch_alter_table('application_upload_files', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('application_upload_files_application_id_fkey', 'applications', ['application_id'], ['id'])

    with op.batch_alter_table('application_types', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('alerts_types', schema=None) as batch_op:
        batch_op.add_column(sa.Column('abbrev', sa.VARCHAR(), autoincrement=False, nullable=True))

    with op.batch_alter_table('alerts_sent_wait_okay', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_sent_wait_okay_alert_type_id_fkey', 'alerts_types', ['alert_type_id'], ['id'])
        batch_op.create_foreign_key('alerts_sent_wait_okay_power_unit_id_fkey', 'power_units', ['power_unit_id'], ['id'])
        batch_op.alter_column('alert_type_id',
               existing_type=sa.INTEGER(),
               nullable=False)

    with op.batch_alter_table('alerts_sent_users', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_sent_users_alerts_sent_id_fkey', 'alerts_sent', ['alerts_sent_id'], ['id'])
        batch_op.alter_column('msg_type',
               existing_type=sa.VARCHAR(),
               comment='email, SMS, phone, WhatsApp, etc',
               existing_nullable=True)

    with op.batch_alter_table('alerts_sent_other', schema=None) as batch_op:
        batch_op.create_table_comment(
        'For saving datetimes when alerts were sent, for other, random stuff like users with no phone numbers, or units with no alerts setup.',
        existing_comment=None
    )

    with op.batch_alter_table('alerts_sent_maint_users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('msg_type', sa.VARCHAR(), autoincrement=False, nullable=True, comment='email, SMS, WhatsApp, phone, etc'))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_sent_maint_users_alerts_sent_maint_id_fkey', 'alerts_sent_maint', ['alerts_sent_maint_id'], ['id'])
        batch_op.alter_column('dev_test_prd',
               existing_type=sa.CHAR(length=11),
               nullable=False)

    with op.batch_alter_table('alerts_sent_maint', schema=None) as batch_op:
        batch_op.add_column(sa.Column('email_type', sa.TEXT(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('customer', sa.TEXT(), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_sent_maint_email_type_id_fkey', 'alerts_sent_maint_email_types', ['email_type_id'], ['id'])
        batch_op.create_foreign_key('alerts_sent_maint_fk_customer_id', 'customers', ['customer_id'], ['id'], referent_schema='myijack')
        batch_op.create_unique_constraint('alerts_sent_maint_timestamp_utc_customer_id_email_type_key', ['timestamp_utc', 'customer_id', 'email_type'])

    with op.batch_alter_table('alerts_sent', schema=None) as batch_op:
        batch_op.create_table_comment(
        'For saving a record of alerts sent',
        existing_comment=None
    )
        batch_op.create_index('alerts_sent_index_timestamp_utc_gateway', ['gateway', 'timestamp_utc'], unique=False)

    with op.batch_alter_table('alerts_custom_user_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_custom_user_rel_alerts_custom_id_fkey', 'alerts_custom', ['alerts_custom_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom_structure_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_custom_structure_rel_structure_id_fkey', 'structures', ['structure_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('alerts_custom_structure_rel_alerts_custom_id_fkey', 'alerts_custom', ['alerts_custom_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom_months_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_custom_months_rel_month_fkey', 'months', ['month'], ['month'])
        batch_op.create_foreign_key('alerts_custom_months_rel_alerts_custom_id_fkey1', 'alerts_custom', ['alerts_custom_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom_images_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_custom_images_rel_alerts_custom_id_fkey', 'alerts_custom', ['alerts_custom_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('alerts_custom_images_rel_image_id_fkey', 'images', ['image_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom_days_rel', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_custom_days_rel_day_fkey', 'days', ['day'], ['day'])
        batch_op.create_foreign_key('alerts_custom_days_rel_alerts_custom_id_fkey', 'alerts_custom', ['alerts_custom_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('alerts_custom', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_custom_fk_customer_id', 'customers', ['customer_id'], ['id'], referent_schema='myijack')
        batch_op.create_foreign_key('alerts_custom_hour_ending_fkey', 'hours', ['hour_ending'], ['hour_ending'])
        batch_op.create_foreign_key('alerts_custom_fk_hour', 'hours', ['hour_ending'], ['hour'])
        batch_op.alter_column('want_email',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('want_sms',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))

    with op.batch_alter_table('alerts', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('alerts_fk_power_unit_id', 'power_units', ['power_unit_id'], ['id'])
        batch_op.create_foreign_key('alerts_fk_gateway_id', 'gw', ['gateway_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('alerts_fk_user_id', 'users', ['user_id'], ['id'], referent_schema='myijack', onupdate='CASCADE', ondelete='CASCADE')
        batch_op.create_foreign_key('alerts_power_unit_id_fkey', 'power_units', ['power_unit_id'], ['id'], ondelete='CASCADE')

    # ### end Alembic commands ###


def upgrade_timescale():
    pass


def downgrade_timescale():
    pass

